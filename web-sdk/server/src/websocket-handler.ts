/**
 * WebSocket Handler for SmartSpectra Server
 * Handles real-time video frame processing and result streaming
 */

import WebSocket from 'ws';
import { IncomingMessage } from 'http';
import { SessionManager } from './session-manager';
import { WebSocketMessage, ServerConfig } from './types';
import sharp from 'sharp';

export class WebSocketHandler {
  private wss: WebSocket.Server;
  private sessionManager: SessionManager;
  private config: ServerConfig;
  private connections = new Map<string, WebSocket>();

  constructor(sessionManager: SessionManager, config: ServerConfig) {
    this.sessionManager = sessionManager;
    this.config = config;
    this.setupWebSocketServer();
    this.setupSessionEvents();
  }

  private setupWebSocketServer(): void {
    this.wss = new WebSocket.Server({
      port: this.config.port + 1, // Use port + 1 for WebSocket
      path: '/ws',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    console.log(`WebSocket server listening on port ${this.config.port + 1}`);
  }

  private verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): boolean {
    // Basic verification - in production, add proper authentication
    const url = new URL(info.req.url || '', `http://${info.req.headers.host}`);
    const sessionId = url.pathname.split('/').pop();
    
    if (!sessionId) {
      return false;
    }

    // Check if session exists
    const session = this.sessionManager.getSession(sessionId);
    return session !== undefined;
  }

  private handleConnection(ws: WebSocket, req: IncomingMessage): void {
    const url = new URL(req.url || '', `http://${req.headers.host}`);
    const sessionId = url.pathname.split('/').pop();

    if (!sessionId) {
      ws.close(1008, 'Invalid session ID');
      return;
    }

    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      ws.close(1008, 'Session not found');
      return;
    }

    console.log(`WebSocket connected for session ${sessionId}`);
    this.connections.set(sessionId, ws);

    // Set up message handling
    ws.on('message', (data: WebSocket.Data) => {
      this.handleMessage(sessionId, data);
    });

    ws.on('close', (code: number, reason: string) => {
      console.log(`WebSocket disconnected for session ${sessionId}: ${code} ${reason}`);
      this.connections.delete(sessionId);
    });

    ws.on('error', (error: Error) => {
      console.error(`WebSocket error for session ${sessionId}:`, error);
      this.connections.delete(sessionId);
    });

    // Send initial connection confirmation
    this.sendMessage(sessionId, {
      type: 'status',
      data: { status: 'connected', sessionId },
      timestamp: Date.now()
    });
  }

  private async handleMessage(sessionId: string, data: WebSocket.Data): Promise<void> {
    try {
      let message: WebSocketMessage;

      if (Buffer.isBuffer(data)) {
        // Handle binary frame data
        await this.handleFrameData(sessionId, data);
        return;
      }

      // Handle JSON messages
      if (typeof data === 'string') {
        message = JSON.parse(data);
      } else {
        console.error('Unsupported message format');
        return;
      }

      switch (message.type) {
        case 'start':
          this.handleStartCommand(sessionId, message);
          break;
        case 'stop':
          this.handleStopCommand(sessionId, message);
          break;
        case 'config':
          this.handleConfigCommand(sessionId, message);
          break;
        default:
          console.warn(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      this.sendError(sessionId, 'MESSAGE_PROCESSING_ERROR', 'Failed to process message');
    }
  }

  private async handleFrameData(sessionId: string, frameBuffer: Buffer): Promise<void> {
    try {
      // Parse frame header (first 16 bytes: width, height, timestamp, format)
      if (frameBuffer.length < 16) {
        throw new Error('Invalid frame data: too short');
      }

      const width = frameBuffer.readUInt32LE(0);
      const height = frameBuffer.readUInt32LE(4);
      const timestamp = frameBuffer.readDoubleLE(8);
      
      // Extract pixel data (skip header)
      const pixelData = frameBuffer.slice(16);

      // Convert to RGB if needed using Sharp
      let rgbData: Buffer;
      if (pixelData.length === width * height * 4) {
        // RGBA to RGB conversion
        rgbData = await sharp(pixelData, {
          raw: { width, height, channels: 4 }
        })
        .removeAlpha()
        .raw()
        .toBuffer();
      } else if (pixelData.length === width * height * 3) {
        // Already RGB
        rgbData = pixelData;
      } else {
        throw new Error(`Invalid pixel data length: expected ${width * height * 3} or ${width * height * 4}, got ${pixelData.length}`);
      }

      // Process frame through C++ SDK
      const success = this.sessionManager.processFrame(sessionId, rgbData, width, height, timestamp);
      
      if (!success) {
        this.sendError(sessionId, 'FRAME_PROCESSING_FAILED', 'Failed to process video frame');
      }

    } catch (error) {
      console.error('Frame processing error:', error);
      this.sendError(sessionId, 'FRAME_PROCESSING_ERROR', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  private handleStartCommand(sessionId: string, message: WebSocketMessage): void {
    const success = this.sessionManager.startSession(sessionId);
    
    this.sendMessage(sessionId, {
      type: 'status',
      data: { 
        status: success ? 'started' : 'failed',
        message: success ? 'Processing started' : 'Failed to start processing'
      },
      timestamp: Date.now()
    });
  }

  private handleStopCommand(sessionId: string, message: WebSocketMessage): void {
    const success = this.sessionManager.stopSession(sessionId);
    
    this.sendMessage(sessionId, {
      type: 'status',
      data: { 
        status: success ? 'stopped' : 'failed',
        message: success ? 'Processing stopped' : 'Failed to stop processing'
      },
      timestamp: Date.now()
    });
  }

  private handleConfigCommand(sessionId: string, message: WebSocketMessage): void {
    // Configuration updates would be handled here
    // For now, just acknowledge
    this.sendMessage(sessionId, {
      type: 'status',
      data: { status: 'config_received' },
      timestamp: Date.now()
    });
  }

  private setupSessionEvents(): void {
    // Listen for metrics from session manager
    this.sessionManager.on('metrics', (sessionId: string, metrics: any) => {
      this.sendMessage(sessionId, {
        type: 'metrics',
        data: metrics,
        timestamp: Date.now()
      });
    });

    // Listen for session errors
    this.sessionManager.on('error', (sessionId: string, error: any) => {
      this.sendError(sessionId, 'PROCESSING_ERROR', error.message || 'Processing error occurred');
    });

    // Listen for session status changes
    this.sessionManager.on('sessionStarted', (sessionId: string, info: any) => {
      this.sendMessage(sessionId, {
        type: 'status',
        data: { status: 'processing_started', ...info },
        timestamp: Date.now()
      });
    });

    this.sessionManager.on('sessionStopped', (sessionId: string) => {
      this.sendMessage(sessionId, {
        type: 'status',
        data: { status: 'processing_stopped' },
        timestamp: Date.now()
      });
    });
  }

  private sendMessage(sessionId: string, message: WebSocketMessage): void {
    const ws = this.connections.get(sessionId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error(`Failed to send message to session ${sessionId}:`, error);
      }
    }
  }

  private sendError(sessionId: string, code: string, message: string): void {
    this.sendMessage(sessionId, {
      type: 'error',
      data: {
        code,
        message,
        timestamp: Date.now()
      },
      timestamp: Date.now()
    });
  }

  public closeConnection(sessionId: string): void {
    const ws = this.connections.get(sessionId);
    if (ws) {
      ws.close(1000, 'Session ended');
      this.connections.delete(sessionId);
    }
  }

  public shutdown(): void {
    // Close all connections
    for (const [sessionId, ws] of this.connections) {
      ws.close(1001, 'Server shutdown');
    }
    this.connections.clear();

    // Close WebSocket server
    this.wss.close();
  }
}
