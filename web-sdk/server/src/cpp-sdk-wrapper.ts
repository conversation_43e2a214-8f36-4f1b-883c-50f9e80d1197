/**
 * C++ SDK Wrapper for Node.js
 * Provides a TypeScript interface to the SmartSpectra C++ SDK
 */

import { EventEmitter } from 'events';
import { CppSdkConfig, VideoFrame, MetricsBuffer, StatusCode } from './types';

// This will be implemented as a native Node.js addon
// For now, we'll create the interface and a mock implementation
interface NativeCppSdk {
  initialize(config: string): boolean;
  isInitialized(): boolean;
  startSpotMode(duration: number): boolean;
  startContinuousMode(): boolean;
  stop(): boolean;
  isRunning(): boolean;
  processFrame(pixelData: Buffer, width: number, height: number, timestamp: number): boolean;
  getCurrentStatus(): number;
  getLastError(): string;
  cleanup(): void;
}

export class CppSdkWrapper extends EventEmitter {
  private nativeInstance: NativeCppSdk | null = null;
  private initialized = false;
  private running = false;
  private config: CppSdkConfig | null = null;

  constructor() {
    super();
    this.loadNativeModule();
  }

  private loadNativeModule(): void {
    try {
      // Try to load the compiled C++ addon first
      try {
        const nativeModule = require('../build/Release/smartspectra_native.node');
        this.nativeInstance = new nativeModule.BackgroundContainerWrapper();
        console.log('Loaded native C++ SDK module');
        return;
      } catch (nativeError) {
        console.warn('Native C++ SDK module not available, falling back to mock:', nativeError.message);
      }

      // Fall back to mock implementation for development/testing
      this.nativeInstance = this.createMockImplementation();
    } catch (error) {
      console.error('Failed to load any SDK module:', error);
      throw new Error('C++ SDK module not available');
    }
  }

  private createMockImplementation(): NativeCppSdk {
    // Mock implementation for development/testing
    return {
      initialize: (config: string) => {
        console.log('Mock: Initializing C++ SDK with config:', config);
        return true;
      },
      isInitialized: () => true,
      startSpotMode: (duration: number) => {
        console.log(`Mock: Starting spot mode for ${duration} seconds`);
        return true;
      },
      startContinuousMode: () => {
        console.log('Mock: Starting continuous mode');
        return true;
      },
      stop: () => {
        console.log('Mock: Stopping processing');
        return true;
      },
      isRunning: () => this.running,
      processFrame: (pixelData: Buffer, width: number, height: number, timestamp: number) => {
        // Mock frame processing - generate simulated metrics
        setTimeout(() => {
          this.generateMockMetrics(timestamp);
        }, 10);
        return true;
      },
      getCurrentStatus: () => 0, // OK status
      getLastError: () => '',
      cleanup: () => {
        console.log('Mock: Cleaning up C++ SDK');
      }
    };
  }

  private generateMockMetrics(timestamp: number): void {
    // Generate simulated metrics for testing
    const mockMetrics: MetricsBuffer = {
      pulse: {
        rate: [{
          time: timestamp,
          value: 70 + Math.random() * 20, // 70-90 BPM
          confidence: 0.8 + Math.random() * 0.2,
          stable: true
        }],
        trace: [{
          time: timestamp,
          value: Math.sin(timestamp / 1000) * 100,
          stable: true
        }]
      },
      breathing: {
        rate: [{
          time: timestamp,
          value: 15 + Math.random() * 5, // 15-20 breaths per minute
          confidence: 0.7 + Math.random() * 0.3,
          stable: true
        }],
        upperTrace: [{
          time: timestamp,
          value: Math.sin(timestamp / 2000) * 50,
          stable: true
        }],
        lowerTrace: [{
          time: timestamp,
          value: Math.cos(timestamp / 2000) * 50,
          stable: true
        }]
      },
      metadata: {
        id: `frame_${timestamp}`,
        frameTimestamp: timestamp,
        frameCount: Math.floor(timestamp / 33), // ~30 FPS
        sentAtS: Date.now() / 1000
      }
    };

    this.emit('metrics', mockMetrics);
  }

  public initialize(config: CppSdkConfig): boolean {
    if (!this.nativeInstance) {
      throw new Error('Native C++ SDK module not loaded');
    }

    try {
      const configJson = JSON.stringify(config);
      const success = this.nativeInstance.initialize(configJson);

      if (success) {
        this.initialized = true;
        this.config = config;
      }

      return success;
    } catch (error) {
      console.error('Failed to initialize C++ SDK:', error);
      return false;
    }
  }

  public isInitialized(): boolean {
    return this.initialized && this.nativeInstance?.isInitialized() === true;
  }

  public startSpotMode(duration: number): boolean {
    if (!this.isInitialized() || !this.nativeInstance) {
      return false;
    }

    const success = this.nativeInstance.startSpotMode(duration);
    if (success) {
      this.running = true;
      this.emit('started', { mode: 'spot', duration });
    }

    return success;
  }

  public startContinuousMode(): boolean {
    if (!this.isInitialized() || !this.nativeInstance) {
      return false;
    }

    const success = this.nativeInstance.startContinuousMode();
    if (success) {
      this.running = true;
      this.emit('started', { mode: 'continuous' });
    }

    return success;
  }

  public stop(): boolean {
    if (!this.nativeInstance) {
      return false;
    }

    const success = this.nativeInstance.stop();
    if (success) {
      this.running = false;
      this.emit('stopped');
    }

    return success;
  }

  public isRunning(): boolean {
    return this.running && this.nativeInstance?.isRunning() === true;
  }

  public processFrame(frame: VideoFrame): boolean {
    if (!this.isRunning() || !this.nativeInstance) {
      return false;
    }

    try {
      return this.nativeInstance.processFrame(
        frame.data,
        frame.width,
        frame.height,
        frame.timestamp
      );
    } catch (error) {
      console.error('Frame processing error:', error);
      this.emit('error', error);
      return false;
    }
  }

  public getCurrentStatus(): StatusCode {
    if (!this.nativeInstance) {
      return StatusCode.PROCESSING_NOT_STARTED;
    }

    const statusCode = this.nativeInstance.getCurrentStatus();
    return this.mapStatusCode(statusCode);
  }

  public getLastError(): string {
    return this.nativeInstance?.getLastError() || '';
  }

  public cleanup(): void {
    if (this.nativeInstance) {
      this.nativeInstance.cleanup();
      this.nativeInstance = null;
    }
    this.initialized = false;
    this.running = false;
    this.config = null;
    this.removeAllListeners();
  }

  private mapStatusCode(code: number): StatusCode {
    const statusMap: Record<number, StatusCode> = {
      0: StatusCode.OK,
      1: StatusCode.NO_FACES_FOUND,
      2: StatusCode.MORE_THAN_ONE_FACE_FOUND,
      3: StatusCode.FACE_NOT_CENTERED,
      4: StatusCode.FACE_TOO_BIG_OR_TOO_SMALL,
      5: StatusCode.IMAGE_TOO_DARK,
      6: StatusCode.IMAGE_TOO_BRIGHT,
      7: StatusCode.CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING,
      8: StatusCode.PROCESSING_NOT_STARTED
    };
    return statusMap[code] || StatusCode.PROCESSING_NOT_STARTED;
  }
}
