/**
 * SmartSpectra Server
 * Main entry point for the server-side processing component
 */

import { SessionManager } from './session-manager';
import { RestApi } from './rest-api';
import { WebSocketHandler } from './websocket-handler';
import { ServerConfig } from './types';

class SmartSpectraServer {
  private sessionManager: SessionManager;
  private restApi: RestApi;
  private webSocketHandler: WebSocketHandler;
  private config: ServerConfig;

  constructor(config: Partial<ServerConfig> = {}) {
    // Default configuration
    this.config = {
      port: 8080,
      host: '0.0.0.0',
      corsOrigins: ['http://localhost:3000', 'http://localhost:8080'],
      maxConnections: 100,
      frameTimeout: 5000,
      sessionTimeout: 300000, // 5 minutes
      debug: process.env.NODE_ENV !== 'production',
      ...config
    };

    this.sessionManager = new SessionManager(this.config.sessionTimeout);
    this.restApi = new RestApi(this.sessionManager, this.config);
    this.webSocketHandler = new WebSocketHandler(this.sessionManager, this.config);

    this.setupGracefulShutdown();
  }

  public async start(): Promise<void> {
    try {
      console.log('Starting SmartSpectra Server...');
      console.log(`Configuration:`, {
        port: this.config.port,
        host: this.config.host,
        debug: this.config.debug,
        maxConnections: this.config.maxConnections
      });

      // Start REST API server
      await this.restApi.start();
      
      console.log('SmartSpectra Server started successfully');
      console.log(`REST API: http://${this.config.host}:${this.config.port}`);
      console.log(`WebSocket: ws://${this.config.host}:${this.config.port + 1}/ws`);
      
    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    console.log('Stopping SmartSpectra Server...');
    
    try {
      // Shutdown components in reverse order
      this.webSocketHandler.shutdown();
      this.sessionManager.shutdown();
      
      console.log('SmartSpectra Server stopped successfully');
    } catch (error) {
      console.error('Error during server shutdown:', error);
    }
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`Received ${signal}, shutting down gracefully...`);
      await this.stop();
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const config: Partial<ServerConfig> = {
    port: parseInt(process.env.PORT || '8080'),
    host: process.env.HOST || '0.0.0.0',
    debug: process.env.DEBUG === 'true',
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    maxConnections: parseInt(process.env.MAX_CONNECTIONS || '100'),
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '300000')
  };

  const server = new SmartSpectraServer(config);
  server.start().catch(console.error);
}

export { SmartSpectraServer };
export * from './types';
export * from './session-manager';
export * from './rest-api';
export * from './websocket-handler';
export * from './cpp-sdk-wrapper';
