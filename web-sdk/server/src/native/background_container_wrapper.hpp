#pragma once

#include <napi.h>
#include <memory>
#include <string>
#include <functional>

// SmartSpectra C++ SDK includes
#include "smartspectra/container/background_container.hpp"
#include "smartspectra/container/settings.hpp"
#include "smartspectra/physiology/status_code.hpp"

namespace smartspectra_native {

/**
 * Node.js wrapper for SmartSpectra C++ BackgroundContainer
 * Provides a JavaScript-accessible interface to the real C++ SDK
 */
class BackgroundContainerWrapper : public Napi::ObjectWrap<BackgroundContainerWrapper> {
public:
    static Napi::Object Init(Napi::Env env, Napi::Object exports);
    BackgroundContainerWrapper(const Napi::CallbackInfo& info);
    ~BackgroundContainerWrapper();

private:
    // JavaScript-accessible methods
    Napi::Value Initialize(const Napi::CallbackInfo& info);
    Napi::Value IsInitialized(const Napi::CallbackInfo& info);
    Napi::Value StartSpotMode(const Napi::CallbackInfo& info);
    Napi::Value StartContinuousMode(const Napi::CallbackInfo& info);
    Napi::Value Stop(const Napi::CallbackInfo& info);
    Napi::Value IsRunning(const Napi::CallbackInfo& info);
    Napi::Value ProcessFrame(const Napi::CallbackInfo& info);
    Napi::Value GetCurrentStatus(const Napi::CallbackInfo& info);
    Napi::Value GetLastError(const Napi::CallbackInfo& info);
    Napi::Value Cleanup(const Napi::CallbackInfo& info);

    // Callback setters
    Napi::Value SetOnMetricsCallback(const Napi::CallbackInfo& info);
    Napi::Value SetOnStatusCallback(const Napi::CallbackInfo& info);
    Napi::Value SetOnErrorCallback(const Napi::CallbackInfo& info);

    // Internal methods
    bool ParseConfiguration(const std::string& config_json);
    void SetupCallbacks();
    void HandleMetricsOutput(const presage::smartspectra::physiology::MetricsBuffer& metrics, int64_t timestamp);
    void HandleStatusChange(presage::smartspectra::physiology::StatusCode status);
    void SetError(const std::string& error);
    std::string SerializeMetrics(const presage::smartspectra::physiology::MetricsBuffer& metrics);

    // C++ SDK instances
    std::unique_ptr<presage::smartspectra::container::CpuSpotRestBackgroundContainer> spot_container_;
    std::unique_ptr<presage::smartspectra::container::CpuContinuousRestBackgroundContainer> continuous_container_;

    // Configuration and state
    std::string api_key_;
    std::string last_error_;
    bool initialized_;
    bool running_;
    presage::smartspectra::container::settings::OperationMode current_mode_;

    // JavaScript callbacks
    Napi::ThreadSafeFunction metrics_callback_;
    Napi::ThreadSafeFunction status_callback_;
    Napi::ThreadSafeFunction error_callback_;

    // Settings structures
    presage::smartspectra::container::settings::SpotRestSettings spot_settings_;
    presage::smartspectra::container::settings::ContinuousRestSettings continuous_settings_;
};

} // namespace smartspectra_native
