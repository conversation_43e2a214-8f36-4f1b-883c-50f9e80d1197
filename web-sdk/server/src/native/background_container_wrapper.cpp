#include "background_container_wrapper.hpp"
#include <opencv2/opencv.hpp>
#include <json/json.h>

namespace smartspectra_native {

using namespace presage::smartspectra;

Napi::Object BackgroundContainerWrapper::Init(Napi::Env env, Napi::Object exports) {
    Napi::Function func = DefineClass(env, "BackgroundContainerWrapper", {
        InstanceMethod("initialize", &BackgroundContainerWrapper::Initialize),
        InstanceMethod("isInitialized", &BackgroundContainerWrapper::IsInitialized),
        InstanceMethod("startSpotMode", &BackgroundContainerWrapper::StartSpotMode),
        InstanceMethod("startContinuousMode", &BackgroundContainerWrapper::StartContinuousMode),
        InstanceMethod("stop", &BackgroundContainerWrapper::Stop),
        InstanceMethod("isRunning", &BackgroundContainerWrapper::IsRunning),
        InstanceMethod("processFrame", &BackgroundContainerWrapper::ProcessFrame),
        InstanceMethod("getCurrentStatus", &BackgroundContainerWrapper::GetCurrentStatus),
        InstanceMethod("getLastError", &BackgroundContainerWrapper::GetLastError),
        InstanceMethod("cleanup", &BackgroundContainerWrapper::Cleanup),
        InstanceMethod("setOnMetricsCallback", &BackgroundContainerWrapper::SetOnMetricsCallback),
        InstanceMethod("setOnStatusCallback", &BackgroundContainerWrapper::SetOnStatusCallback),
        InstanceMethod("setOnErrorCallback", &BackgroundContainerWrapper::SetOnErrorCallback)
    });

    Napi::FunctionReference* constructor = new Napi::FunctionReference();
    *constructor = Napi::Persistent(func);
    env.SetInstanceData(constructor);

    exports.Set("BackgroundContainerWrapper", func);
    return exports;
}

BackgroundContainerWrapper::BackgroundContainerWrapper(const Napi::CallbackInfo& info)
    : Napi::ObjectWrap<BackgroundContainerWrapper>(info),
      initialized_(false),
      running_(false),
      current_mode_(container::settings::OperationMode::Spot) {
}

BackgroundContainerWrapper::~BackgroundContainerWrapper() {
    Cleanup(Napi::CallbackInfo(Env(), nullptr));
}

Napi::Value BackgroundContainerWrapper::Initialize(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Configuration JSON string expected").ThrowAsJavaScriptException();
        return Napi::Boolean::New(env, false);
    }

    std::string config_json = info[0].As<Napi::String>().Utf8Value();

    try {
        if (!ParseConfiguration(config_json)) {
            return Napi::Boolean::New(env, false);
        }

        // Initialize appropriate container based on mode
        if (current_mode_ == container::settings::OperationMode::Spot) {
            spot_container_ = std::make_unique<container::CpuSpotRestBackgroundContainer>(spot_settings_);
            auto status = spot_container_->Initialize();
            if (!status.ok()) {
                SetError("Failed to initialize spot container: " + status.ToString());
                return Napi::Boolean::New(env, false);
            }
        } else {
            continuous_container_ = std::make_unique<container::CpuContinuousRestBackgroundContainer>(continuous_settings_);
            auto status = continuous_container_->Initialize();
            if (!status.ok()) {
                SetError("Failed to initialize continuous container: " + status.ToString());
                return Napi::Boolean::New(env, false);
            }
        }

        SetupCallbacks();
        initialized_ = true;
        return Napi::Boolean::New(env, true);

    } catch (const std::exception& e) {
        SetError("Initialization failed: " + std::string(e.what()));
        return Napi::Boolean::New(env, false);
    }
}

Napi::Value BackgroundContainerWrapper::IsInitialized(const Napi::CallbackInfo& info) {
    return Napi::Boolean::New(info.Env(), initialized_);
}

Napi::Value BackgroundContainerWrapper::StartSpotMode(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (!initialized_ || !spot_container_) {
        SetError("Container not initialized for spot mode");
        return Napi::Boolean::New(env, false);
    }

    try {
        auto status = spot_container_->StartGraph();
        if (!status.ok()) {
            SetError("Failed to start spot mode: " + status.ToString());
            return Napi::Boolean::New(env, false);
        }

        running_ = true;
        return Napi::Boolean::New(env, true);

    } catch (const std::exception& e) {
        SetError("Start spot mode failed: " + std::string(e.what()));
        return Napi::Boolean::New(env, false);
    }
}

Napi::Value BackgroundContainerWrapper::StartContinuousMode(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (!initialized_ || !continuous_container_) {
        SetError("Container not initialized for continuous mode");
        return Napi::Boolean::New(env, false);
    }

    try {
        auto status = continuous_container_->StartGraph();
        if (!status.ok()) {
            SetError("Failed to start continuous mode: " + status.ToString());
            return Napi::Boolean::New(env, false);
        }

        running_ = true;
        return Napi::Boolean::New(env, true);

    } catch (const std::exception& e) {
        SetError("Start continuous mode failed: " + std::string(e.what()));
        return Napi::Boolean::New(env, false);
    }
}

Napi::Value BackgroundContainerWrapper::Stop(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (!running_) {
        return Napi::Boolean::New(env, true);
    }

    try {
        absl::Status status;

        if (spot_container_) {
            status = spot_container_->StopGraph();
        } else if (continuous_container_) {
            status = continuous_container_->StopGraph();
        }

        if (!status.ok()) {
            SetError("Failed to stop processing: " + status.ToString());
            return Napi::Boolean::New(env, false);
        }

        running_ = false;
        return Napi::Boolean::New(env, true);

    } catch (const std::exception& e) {
        SetError("Stop failed: " + std::string(e.what()));
        return Napi::Boolean::New(env, false);
    }
}

Napi::Value BackgroundContainerWrapper::IsRunning(const Napi::CallbackInfo& info) {
    bool is_running = running_;

    if (spot_container_) {
        is_running = is_running && spot_container_->GraphIsRunning();
    } else if (continuous_container_) {
        is_running = is_running && continuous_container_->GraphIsRunning();
    }

    return Napi::Boolean::New(info.Env(), is_running);
}

Napi::Value BackgroundContainerWrapper::ProcessFrame(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (info.Length() < 4) {
        Napi::TypeError::New(env, "Expected 4 arguments: buffer, width, height, timestamp").ThrowAsJavaScriptException();
        return Napi::Boolean::New(env, false);
    }

    if (!running_) {
        SetError("Container not running");
        return Napi::Boolean::New(env, false);
    }

    try {
        // Extract frame data
        Napi::Buffer<uint8_t> buffer = info[0].As<Napi::Buffer<uint8_t>>();
        int width = info[1].As<Napi::Number>().Int32Value();
        int height = info[2].As<Napi::Number>().Int32Value();
        double timestamp_ms = info[3].As<Napi::Number>().DoubleValue();

        // Convert to OpenCV Mat (RGB format)
        cv::Mat frame_rgb(height, width, CV_8UC3, buffer.Data());

        // Convert timestamp to microseconds
        int64_t timestamp_us = static_cast<int64_t>(timestamp_ms * 1000);

        // Process frame through appropriate container
        absl::Status status;
        if (spot_container_) {
            status = spot_container_->AddFrameWithTimestamp(frame_rgb, timestamp_us);
        } else if (continuous_container_) {
            status = continuous_container_->AddFrameWithTimestamp(frame_rgb, timestamp_us);
        } else {
            SetError("No container available");
            return Napi::Boolean::New(env, false);
        }

        if (!status.ok()) {
            SetError("Frame processing failed: " + status.ToString());
            return Napi::Boolean::New(env, false);
        }

        return Napi::Boolean::New(env, true);

    } catch (const std::exception& e) {
        SetError("Process frame failed: " + std::string(e.what()));
        return Napi::Boolean::New(env, false);
    }
}

Napi::Value BackgroundContainerWrapper::GetCurrentStatus(const Napi::CallbackInfo& info) {
    physiology::StatusCode status = physiology::StatusCode::PROCESSING_NOT_STARTED;

    if (spot_container_) {
        status = spot_container_->GetStatusCode();
    } else if (continuous_container_) {
        status = continuous_container_->GetStatusCode();
    }

    return Napi::Number::New(info.Env(), static_cast<int>(status));
}

Napi::Value BackgroundContainerWrapper::GetLastError(const Napi::CallbackInfo& info) {
    return Napi::String::New(info.Env(), last_error_);
}

Napi::Value BackgroundContainerWrapper::Cleanup(const Napi::CallbackInfo& info) {
    if (running_) {
        Stop(info);
    }

    spot_container_.reset();
    continuous_container_.reset();

    // Clean up callbacks
    if (metrics_callback_) {
        metrics_callback_.Release();
    }
    if (status_callback_) {
        status_callback_.Release();
    }
    if (error_callback_) {
        error_callback_.Release();
    }

    initialized_ = false;
    running_ = false;
    last_error_.clear();

    return info.Env().Undefined();
}

bool BackgroundContainerWrapper::ParseConfiguration(const std::string& config_json) {
    try {
        Json::Value config;
        Json::Reader reader;

        if (!reader.parse(config_json, config)) {
            SetError("Invalid JSON configuration");
            return false;
        }

        // Extract common settings
        api_key_ = config.get("api_key", "").asString();
        if (api_key_.empty()) {
            SetError("API key is required");
            return false;
        }

        std::string mode_str = config.get("mode", "spot").asString();
        if (mode_str == "spot") {
            current_mode_ = container::settings::OperationMode::Spot;

            // Configure spot settings
            spot_settings_.api_key = api_key_;
            spot_settings_.spot_duration_s = config.get("spot_duration_s", 30.0).asDouble();

        } else if (mode_str == "continuous") {
            current_mode_ = container::settings::OperationMode::Continuous;

            // Configure continuous settings
            continuous_settings_.api_key = api_key_;
            continuous_settings_.preprocessed_data_buffer_duration_s =
                config.get("buffer_duration_s", 10.0).asDouble();
        } else {
            SetError("Invalid mode: " + mode_str);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        SetError("Configuration parsing failed: " + std::string(e.what()));
        return false;
    }
}

void BackgroundContainerWrapper::SetupCallbacks() {
    // Set up callbacks for metrics and status updates
    // This would involve setting up the C++ SDK callbacks to call JavaScript functions
    // Implementation depends on the specific C++ SDK callback mechanisms
}

Napi::Value BackgroundContainerWrapper::SetOnMetricsCallback(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsFunction()) {
        Napi::TypeError::New(env, "Function expected").ThrowAsJavaScriptException();
        return env.Undefined();
    }

    metrics_callback_ = Napi::ThreadSafeFunction::New(
        env,
        info[0].As<Napi::Function>(),
        "MetricsCallback",
        0,
        1
    );

    return env.Undefined();
}

Napi::Value BackgroundContainerWrapper::SetOnStatusCallback(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsFunction()) {
        Napi::TypeError::New(env, "Function expected").ThrowAsJavaScriptException();
        return env.Undefined();
    }

    status_callback_ = Napi::ThreadSafeFunction::New(
        env,
        info[0].As<Napi::Function>(),
        "StatusCallback",
        0,
        1
    );

    return env.Undefined();
}

Napi::Value BackgroundContainerWrapper::SetOnErrorCallback(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsFunction()) {
        Napi::TypeError::New(env, "Function expected").ThrowAsJavaScriptException();
        return env.Undefined();
    }

    error_callback_ = Napi::ThreadSafeFunction::New(
        env,
        info[0].As<Napi::Function>(),
        "ErrorCallback",
        0,
        1
    );

    return env.Undefined();
}

void BackgroundContainerWrapper::SetError(const std::string& error) {
    last_error_ = error;
}

} // namespace smartspectra_native
