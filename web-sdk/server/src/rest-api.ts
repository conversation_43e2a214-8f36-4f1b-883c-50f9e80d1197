/**
 * REST API for SmartSpectra Server
 * Handles HTTP endpoints for session management and configuration
 */

import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { SessionManager } from './session-manager';
import { 
  StartSessionRequest, 
  StartSessionResponse, 
  SessionStatusResponse, 
  ServerError,
  ServerConfig 
} from './types';

export class RestApi {
  private app: express.Application;
  private sessionManager: SessionManager;
  private config: ServerConfig;

  constructor(sessionManager: SessionManager, config: ServerConfig) {
    this.app = express();
    this.sessionManager = sessionManager;
    this.config = config;
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: this.config.corsOrigins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      if (this.config.debug) {
        console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
      }
      next();
    });
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        activeSessions: this.sessionManager.getActiveSessionCount()
      });
    });

    // API info
    this.app.get('/api/info', (req: Request, res: Response) => {
      res.json({
        name: 'SmartSpectra Server',
        version: '1.0.0',
        apiVersion: 'v1',
        capabilities: ['spot', 'continuous'],
        maxConnections: this.config.maxConnections,
        activeSessions: this.sessionManager.getActiveSessionCount()
      });
    });

    // Start processing session
    this.app.post('/api/sessions', this.authenticateApiKey.bind(this), (req: Request, res: Response) => {
      try {
        const request: StartSessionRequest = req.body;
        const apiKey = this.extractApiKey(req);

        if (!request.mode || !request.config) {
          return res.status(400).json(this.createError('INVALID_REQUEST', 'Missing mode or config'));
        }

        // Validate configuration
        if (!request.config.api_key) {
          request.config.api_key = apiKey;
        }

        const sessionId = this.sessionManager.createSession(apiKey, request.mode, request.config);
        
        const response: StartSessionResponse = {
          sessionId,
          websocketUrl: `ws://${this.config.host}:${this.config.port}/ws/${sessionId}`,
          status: 'started'
        };

        res.status(201).json(response);
      } catch (error) {
        console.error('Failed to create session:', error);
        res.status(500).json(this.createError('SESSION_CREATION_FAILED', 'Failed to create processing session'));
      }
    });

    // Get session status
    this.app.get('/api/sessions/:sessionId', this.authenticateApiKey.bind(this), (req: Request, res: Response) => {
      const sessionId = req.params.sessionId;
      const status = this.sessionManager.getSessionStatus(sessionId);

      if (!status) {
        return res.status(404).json(this.createError('SESSION_NOT_FOUND', 'Session not found'));
      }

      const response: SessionStatusResponse = status;
      res.json(response);
    });

    // Start session processing
    this.app.post('/api/sessions/:sessionId/start', this.authenticateApiKey.bind(this), (req: Request, res: Response) => {
      const sessionId = req.params.sessionId;
      const success = this.sessionManager.startSession(sessionId);

      if (!success) {
        return res.status(400).json(this.createError('START_FAILED', 'Failed to start session'));
      }

      res.json({ status: 'started', sessionId });
    });

    // Stop session processing
    this.app.post('/api/sessions/:sessionId/stop', this.authenticateApiKey.bind(this), (req: Request, res: Response) => {
      const sessionId = req.params.sessionId;
      const success = this.sessionManager.stopSession(sessionId);

      if (!success) {
        return res.status(400).json(this.createError('STOP_FAILED', 'Failed to stop session'));
      }

      res.json({ status: 'stopped', sessionId });
    });

    // Delete session
    this.app.delete('/api/sessions/:sessionId', this.authenticateApiKey.bind(this), (req: Request, res: Response) => {
      const sessionId = req.params.sessionId;
      const success = this.sessionManager.destroySession(sessionId);

      if (!success) {
        return res.status(404).json(this.createError('SESSION_NOT_FOUND', 'Session not found'));
      }

      res.status(204).send();
    });

    // List all sessions (admin endpoint)
    this.app.get('/api/sessions', this.authenticateApiKey.bind(this), (req: Request, res: Response) => {
      const sessions = this.sessionManager.getAllSessions();
      res.json({
        sessions: sessions.map(s => ({
          id: s.id,
          mode: s.mode,
          isActive: s.isActive,
          frameCount: s.frameCount,
          startTime: s.startTime,
          lastActivity: s.lastActivity
        })),
        total: sessions.length,
        active: sessions.filter(s => s.isActive).length
      });
    });
  }

  private setupErrorHandling(): void {
    // 404 handler
    this.app.use((req: Request, res: Response) => {
      res.status(404).json(this.createError('NOT_FOUND', 'Endpoint not found'));
    });

    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      console.error('Unhandled error:', error);
      res.status(500).json(this.createError('INTERNAL_ERROR', 'Internal server error'));
    });
  }

  private authenticateApiKey(req: Request, res: Response, next: NextFunction): void {
    const apiKey = this.extractApiKey(req);

    if (!apiKey) {
      return res.status(401).json(this.createError('MISSING_API_KEY', 'API key required'));
    }

    // Basic API key validation (in production, validate against a database)
    if (apiKey.length < 10) {
      return res.status(401).json(this.createError('INVALID_API_KEY', 'Invalid API key format'));
    }

    next();
  }

  private extractApiKey(req: Request): string {
    // Check Authorization header (Bearer token)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check X-API-Key header
    const apiKeyHeader = req.headers['x-api-key'];
    if (apiKeyHeader && typeof apiKeyHeader === 'string') {
      return apiKeyHeader;
    }

    return '';
  }

  private createError(code: string, message: string, details?: any): ServerError {
    return {
      code,
      message,
      details,
      timestamp: Date.now()
    };
  }

  public getApp(): express.Application {
    return this.app;
  }

  public start(): Promise<void> {
    return new Promise((resolve) => {
      this.app.listen(this.config.port, this.config.host, () => {
        console.log(`SmartSpectra Server listening on ${this.config.host}:${this.config.port}`);
        resolve();
      });
    });
  }
}
