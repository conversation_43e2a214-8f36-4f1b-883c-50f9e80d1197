/**
 * Session Manager for SmartSpectra Server
 * Manages processing sessions and their lifecycle
 */

import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { ProcessingSession, SmartSpectraMode, CppSdkConfig } from './types';
import { CppSdkWrapper } from './cpp-sdk-wrapper';

export class SessionManager extends EventEmitter {
  private sessions = new Map<string, ProcessingSession>();
  private sdkInstances = new Map<string, CppSdkWrapper>();
  private sessionTimeout: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor(sessionTimeout = 300000) { // 5 minutes default
    super();
    this.sessionTimeout = sessionTimeout;
    
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60000); // Check every minute
  }

  public createSession(apiKey: string, mode: SmartSpectraMode, config: CppSdkConfig): string {
    const sessionId = uuidv4();
    const now = Date.now();

    const session: ProcessingSession = {
      id: sessionId,
      apiKey,
      mode,
      config,
      startTime: now,
      lastActivity: now,
      isActive: false,
      frameCount: 0
    };

    // Create C++ SDK instance for this session
    const sdkWrapper = new CppSdkWrapper();
    
    // Set up event listeners
    sdkWrapper.on('metrics', (metrics) => {
      this.emit('metrics', sessionId, metrics);
      this.updateSessionActivity(sessionId);
    });

    sdkWrapper.on('error', (error) => {
      this.emit('error', sessionId, error);
    });

    sdkWrapper.on('started', (info) => {
      session.isActive = true;
      this.emit('sessionStarted', sessionId, info);
    });

    sdkWrapper.on('stopped', () => {
      session.isActive = false;
      this.emit('sessionStopped', sessionId);
    });

    // Initialize the SDK
    if (!sdkWrapper.initialize(config)) {
      throw new Error('Failed to initialize C++ SDK for session');
    }

    this.sessions.set(sessionId, session);
    this.sdkInstances.set(sessionId, sdkWrapper);

    console.log(`Created session ${sessionId} for mode ${mode}`);
    return sessionId;
  }

  public startSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    const sdkWrapper = this.sdkInstances.get(sessionId);

    if (!session || !sdkWrapper) {
      return false;
    }

    let success = false;
    if (session.mode === SmartSpectraMode.SPOT) {
      const duration = session.config.spot_duration_s || 30;
      success = sdkWrapper.startSpotMode(duration);
    } else {
      success = sdkWrapper.startContinuousMode();
    }

    if (success) {
      session.isActive = true;
      this.updateSessionActivity(sessionId);
    }

    return success;
  }

  public stopSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    const sdkWrapper = this.sdkInstances.get(sessionId);

    if (!session || !sdkWrapper) {
      return false;
    }

    const success = sdkWrapper.stop();
    if (success) {
      session.isActive = false;
      this.updateSessionActivity(sessionId);
    }

    return success;
  }

  public processFrame(sessionId: string, frameData: Buffer, width: number, height: number, timestamp: number): boolean {
    const session = this.sessions.get(sessionId);
    const sdkWrapper = this.sdkInstances.get(sessionId);

    if (!session || !sdkWrapper || !session.isActive) {
      return false;
    }

    const frame = {
      data: frameData,
      width,
      height,
      timestamp,
      format: 'RGB' as const
    };

    const success = sdkWrapper.processFrame(frame);
    if (success) {
      session.frameCount++;
      this.updateSessionActivity(sessionId);
    }

    return success;
  }

  public getSession(sessionId: string): ProcessingSession | undefined {
    return this.sessions.get(sessionId);
  }

  public getSessionStatus(sessionId: string) {
    const session = this.sessions.get(sessionId);
    const sdkWrapper = this.sdkInstances.get(sessionId);

    if (!session || !sdkWrapper) {
      return null;
    }

    return {
      sessionId,
      isActive: session.isActive,
      mode: session.mode,
      frameCount: session.frameCount,
      duration: (Date.now() - session.startTime) / 1000,
      status: sdkWrapper.getCurrentStatus()
    };
  }

  public destroySession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    const sdkWrapper = this.sdkInstances.get(sessionId);

    if (!session && !sdkWrapper) {
      return false;
    }

    // Stop processing if active
    if (sdkWrapper && session?.isActive) {
      sdkWrapper.stop();
    }

    // Cleanup SDK instance
    if (sdkWrapper) {
      sdkWrapper.cleanup();
      this.sdkInstances.delete(sessionId);
    }

    // Remove session
    if (session) {
      this.sessions.delete(sessionId);
    }

    console.log(`Destroyed session ${sessionId}`);
    this.emit('sessionDestroyed', sessionId);
    return true;
  }

  public getAllSessions(): ProcessingSession[] {
    return Array.from(this.sessions.values());
  }

  public getActiveSessionCount(): number {
    return Array.from(this.sessions.values()).filter(s => s.isActive).length;
  }

  private updateSessionActivity(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastActivity = Date.now();
    }
  }

  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions) {
      if (now - session.lastActivity > this.sessionTimeout) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      console.log(`Cleaning up expired session ${sessionId}`);
      this.destroySession(sessionId);
    }
  }

  public shutdown(): void {
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Destroy all sessions
    const sessionIds = Array.from(this.sessions.keys());
    for (const sessionId of sessionIds) {
      this.destroySession(sessionId);
    }

    this.removeAllListeners();
  }
}
