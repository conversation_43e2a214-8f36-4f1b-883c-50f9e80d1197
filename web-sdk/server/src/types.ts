/**
 * TypeScript definitions for SmartSpectra Server
 * Interfaces for server-side processing and communication
 */

// Core operation types - aligned with C++ SDK
export enum SmartSpectraMode {
  SPOT = 'spot',
  CONTINUOUS = 'continuous'
}

export enum StatusCode {
  OK = 'OK',
  NO_FACES_FOUND = 'NO_FACES_FOUND',
  MORE_THAN_ONE_FACE_FOUND = 'MORE_THAN_ONE_FACE_FOUND',
  FACE_NOT_CENTERED = 'FACE_NOT_CENTERED',
  FACE_TOO_BIG_OR_TOO_SMALL = 'FACE_TOO_BIG_OR_TOO_SMALL',
  IMAGE_TOO_DARK = 'IMAGE_TOO_DARK',
  IMAGE_TOO_BRIGHT = 'IMAGE_TOO_BRIGHT',
  CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING = 'CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING',
  PROCESSING_NOT_STARTED = 'PROCESSING_NOT_STARTED'
}

// Server configuration
export interface ServerConfig {
  port: number;
  host: string;
  corsOrigins: string[];
  maxConnections: number;
  frameTimeout: number;
  sessionTimeout: number;
  debug: boolean;
}

// Session management
export interface ProcessingSession {
  id: string;
  apiKey: string;
  mode: SmartSpectraMode;
  config: CppSdkConfig;
  startTime: number;
  lastActivity: number;
  isActive: boolean;
  frameCount: number;
}

// C++ SDK configuration (maps to C++ Settings structures)
export interface CppSdkConfig {
  mode: 'spot' | 'continuous';
  spot_duration_s?: number;
  buffer_duration_s?: number;
  api_key: string;
  scale_input?: boolean;
  enable_phasic_bp?: boolean;
  enable_dense_facemesh_points?: boolean;
  use_full_range_face_detection?: boolean;
  enable_edge_metrics?: boolean;
  verbosity_level?: number;
}

// Video frame data
export interface VideoFrame {
  data: Buffer;
  width: number;
  height: number;
  timestamp: number;
  format: 'RGB' | 'RGBA' | 'BGR';
}

// Processing results (from C++ SDK)
export interface MetricsBuffer {
  pulse?: {
    rate: Array<{ time: number; value: number; confidence: number; stable: boolean }>;
    trace: Array<{ time: number; value: number; stable: boolean }>;
  };
  breathing?: {
    rate: Array<{ time: number; value: number; confidence: number; stable: boolean }>;
    upperTrace: Array<{ time: number; value: number; stable: boolean }>;
    lowerTrace: Array<{ time: number; value: number; stable: boolean }>;
  };
  face?: {
    landmarks: Array<{
      time: number;
      value: Array<{ x: number; y: number }>;
      stable: boolean;
    }>;
  };
  metadata: {
    id: string;
    frameTimestamp: number;
    frameCount: number;
    sentAtS: number;
  };
}

// WebSocket message types
export interface WebSocketMessage {
  type: 'frame' | 'start' | 'stop' | 'config' | 'status' | 'metrics' | 'error';
  sessionId?: string;
  data?: any;
  timestamp: number;
}

// REST API request/response types
export interface StartSessionRequest {
  mode: SmartSpectraMode;
  config: CppSdkConfig;
}

export interface StartSessionResponse {
  sessionId: string;
  websocketUrl: string;
  status: 'started' | 'error';
  message?: string;
}

export interface SessionStatusResponse {
  sessionId: string;
  isActive: boolean;
  mode: SmartSpectraMode;
  frameCount: number;
  duration: number;
  status: StatusCode;
}

// Error types
export interface ServerError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}
