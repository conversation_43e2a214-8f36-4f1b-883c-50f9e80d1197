{
  "targets": [
    {
      "target_name": "smartspectra_native",
      "sources": [
        "src/native/smartspectra_native.cpp",
        "src/native/background_container_wrapper.cpp"
      ],
      "include_dirs": [
        "<!@(node -p \"require('node-addon-api').include\")",
        "../../cpp",
        "../../cpp/smartspectra",
        "../../cpp/smartspectra/container"
      ],
      "dependencies": [
        "<!(node -p \"require('node-addon-api').gyp\")"
      ],
      "cflags!": [ "-fno-exceptions" ],
      "cflags_cc!": [ "-fno-exceptions" ],
      "xcode_settings": {
        "GCC_ENABLE_CPP_EXCEPTIONS": "YES",
        "CLANG_CXX_LIBRARY": "libc++",
        "MACOSX_DEPLOYMENT_TARGET": "10.7"
      },
      "msvs_settings": {
        "VCCLCompilerTool": { "ExceptionHandling": 1 }
      },
      "libraries": [
        # Link against the C++ SDK libraries
        # These paths will need to be adjusted based on your build
        "../../cpp/build/lib/libsmartspectra.a",
        # Add other required libraries (OpenCV, MediaPipe, etc.)
      ],
      "defines": [
        "NAPI_DISABLE_CPP_EXCEPTIONS"
      ]
    }
  ]
}
