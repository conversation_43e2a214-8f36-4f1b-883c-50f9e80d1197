# SmartSpectra Server

Server-side component for SmartSpectra that integrates with the C++ SDK to provide real-time video processing capabilities through REST API and WebSocket interfaces.

## Features

- **Real-time Processing**: WebSocket-based video frame processing using the actual C++ SDK
- **REST API**: HTTP endpoints for session management and configuration
- **Session Management**: Automatic session lifecycle management with timeout handling
- **Multiple Modes**: Support for both spot and continuous measurement modes
- **Scalable Architecture**: Designed to handle multiple concurrent processing sessions

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │  SmartSpectra   │    │   C++ SDK       │
│                 │    │     Server      │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ JavaScript  │◄├────┤ │ REST API    │ │    │ │Background   │ │
│ │ SDK         │ │    │ │             │ │    │ │Container    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │ Video       │◄├────┤ │ WebSocket   │◄├────┤                 │
│ │ Capture     │ │    │ │ Handler     │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Installation

```bash
cd web-sdk/server
npm install
```

## Configuration

The server can be configured through environment variables or configuration object:

```bash
# Environment Variables
PORT=8080                    # Server port
HOST=0.0.0.0                # Server host
DEBUG=true                  # Enable debug logging
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
MAX_CONNECTIONS=100         # Maximum concurrent connections
SESSION_TIMEOUT=300000      # Session timeout in milliseconds
```

## Usage

### Development

```bash
npm run dev
```

### Production

```bash
npm run build
npm start
```

## API Reference

### REST Endpoints

#### Health Check
```
GET /health
```

#### Create Session
```
POST /api/sessions
Content-Type: application/json
Authorization: Bearer <api-key>

{
  "mode": "spot",
  "config": {
    "api_key": "your-api-key",
    "spot_duration_s": 30,
    "enable_edge_metrics": true
  }
}
```

#### Get Session Status
```
GET /api/sessions/:sessionId
Authorization: Bearer <api-key>
```

#### Start Processing
```
POST /api/sessions/:sessionId/start
Authorization: Bearer <api-key>
```

#### Stop Processing
```
POST /api/sessions/:sessionId/stop
Authorization: Bearer <api-key>
```

### WebSocket Interface

Connect to: `ws://localhost:8081/ws/:sessionId`

#### Message Types

**Start Processing**
```json
{
  "type": "start",
  "timestamp": **********
}
```

**Stop Processing**
```json
{
  "type": "stop",
  "timestamp": **********
}
```

**Video Frame** (Binary)
Frame format: [width:4][height:4][timestamp:8][pixel_data:width*height*3]

**Metrics Response**
```json
{
  "type": "metrics",
  "data": {
    "pulse": {
      "rate": [{"time": **********, "value": 72.5, "confidence": 0.95}]
    },
    "breathing": {
      "rate": [{"time": **********, "value": 16.2, "confidence": 0.88}]
    }
  },
  "timestamp": **********
}
```

## C++ SDK Integration

The server integrates with the SmartSpectra C++ SDK through a Node.js native addon. The addon provides:

- Direct access to `BackgroundContainer` classes
- Real-time frame processing using `AddFrameWithTimestamp`
- Callback-based metrics and status updates
- Memory-efficient pixel data handling

### Building the Native Addon

```bash
# Install dependencies
npm install node-gyp -g

# Build the native addon (requires C++ SDK)
npm run build:native
```

## Development

### Project Structure

```
server/
├── src/
│   ├── index.ts              # Main server entry point
│   ├── types.ts              # TypeScript definitions
│   ├── session-manager.ts    # Session lifecycle management
│   ├── rest-api.ts           # HTTP REST API endpoints
│   ├── websocket-handler.ts  # WebSocket frame processing
│   └── cpp-sdk-wrapper.ts    # C++ SDK integration layer
├── package.json
├── tsconfig.json
└── README.md
```

### Testing

```bash
npm test
```

### Linting

```bash
npm run lint
npm run lint:fix
```

## Deployment

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
EXPOSE 8080 8081
CMD ["npm", "start"]
```

### Environment Setup

1. Ensure C++ SDK is built and available
2. Install Node.js 18+ 
3. Configure environment variables
4. Build and start the server

## Security Considerations

- API key authentication required for all endpoints
- CORS configuration for web client origins
- Session timeout to prevent resource leaks
- Input validation for video frame data
- Rate limiting (recommended for production)

## Performance

- Concurrent session support
- Efficient binary frame processing
- Memory management for video data
- Automatic cleanup of expired sessions

## Troubleshooting

### Common Issues

1. **C++ SDK not found**: Ensure the native addon is built correctly
2. **WebSocket connection failed**: Check CORS and firewall settings
3. **Frame processing errors**: Verify video frame format and dimensions
4. **Session timeout**: Adjust `SESSION_TIMEOUT` environment variable
