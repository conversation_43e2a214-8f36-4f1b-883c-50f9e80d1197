{"name": "@smartspectra/server", "version": "1.0.0", "description": "SmartSpectra Server for real-time video processing using C++ SDK", "main": "dist/index.js", "scripts": {"build": "npm run build:native && npm run build:ts", "build:ts": "tsc", "build:native": "node-gyp rebuild", "build:native:debug": "node-gyp rebuild --debug", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "ts-node --watch src/index.ts", "test": "jest", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "clean": "rimraf dist/ build/", "install": "npm run build:native || echo 'Native build failed, will use mock implementation'"}, "keywords": ["smartspectra", "server", "websocket", "rest-api", "video-processing", "cpp-sdk"], "author": "Presage Technologies", "license": "SEE LICENSE IN LICENSE", "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "uuid": "^9.0.1", "node-addon-api": "^7.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/ws": "^8.5.8", "@types/cors": "^2.8.15", "@types/multer": "^1.4.9", "@types/uuid": "^9.0.6", "@types/node": "^20.0.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "ts-jest": "^29.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.0", "rimraf": "^5.0.0", "node-gyp": "^10.0.0"}, "engines": {"node": ">=18.0.0"}}