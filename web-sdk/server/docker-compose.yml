version: '3.8'

services:
  smartspectra-server:
    build: .
    ports:
      - "8080:8080"  # REST API
      - "8081:8081"  # WebSocket
    environment:
      - NODE_ENV=production
      - PORT=8080
      - HOST=0.0.0.0
      - DEBUG=false
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8080
      - MAX_CONNECTIONS=100
      - SESSION_TIMEOUT=300000
    volumes:
      # Mount C++ SDK if available
      - ../../cpp:/app/cpp:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - smartspectra-server
    restart: unless-stopped
    profiles:
      - production
