# SmartSpectra Server Dockerfile
FROM node:18-alpine

# Install build dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cmake \
    git \
    pkgconfig \
    opencv-dev

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY dist/ ./dist/
COPY src/native/ ./src/native/
COPY binding.gyp ./

# Try to build native module (optional - will fall back to mock if fails)
RUN npm run build:native || echo "Native build failed, will use mock implementation"

# Create non-root user
RUN addgroup -g 1001 -S smartspectra && \
    adduser -S smartspectra -u 1001

# Change ownership of app directory
RUN chown -R smartspectra:smartspectra /app

# Switch to non-root user
USER smartspectra

# Expose ports
EXPOSE 8080 8081

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:8080/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the server
CMD ["npm", "start"]
