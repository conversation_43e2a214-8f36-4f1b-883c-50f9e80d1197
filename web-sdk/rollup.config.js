import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
// import copy from 'rollup-plugin-copy'; // Removed - no longer needed

const external = [];

const plugins = [
  resolve({
    browser: true,
    preferBuiltins: false,
  }),
  commonjs(),
  typescript({
    tsconfig: './tsconfig.json',
    declaration: true,
    declarationDir: './dist',
  }),
  // Copy plugin removed - no WASM files to copy
];

export default [
  // ES Module build
  {
    input: 'src/js/index.ts',
    output: {
      file: 'dist/index.esm.js',
      format: 'es',
      sourcemap: true,
    },
    external,
    plugins,
  },
  // CommonJS build
  {
    input: 'src/js/index.ts',
    output: {
      file: 'dist/index.js',
      format: 'cjs',
      sourcemap: true,
    },
    external,
    plugins,
  },
  // UMD build for browsers
  {
    input: 'src/js/index.ts',
    output: {
      file: 'dist/index.umd.js',
      format: 'umd',
      name: 'SmartSpectraWebSDK',
      sourcemap: true,
    },
    external,
    plugins,
  },
];
