(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
    typeof define === 'function' && define.amd ? define(['exports'], factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.SmartSpectraWebSDK = {}));
})(this, (function (exports) { 'use strict';

    /**
     * TypeScript definitions for SmartSpectra WASM-JS SDK
     * Aligned with protobuf definitions and native SDK interfaces
     */
    // Core operation types - aligned with Swift/Android naming
    exports.SmartSpectraMode = void 0;
    (function (SmartSpectraMode) {
        SmartSpectraMode["SPOT"] = "spot";
        SmartSpectraMode["CONTINUOUS"] = "continuous";
    })(exports.SmartSpectraMode || (exports.SmartSpectraMode = {}));
    // Status codes matching C++ physiology::StatusCode enum exactly
    exports.StatusCode = void 0;
    (function (StatusCode) {
        StatusCode["OK"] = "OK";
        StatusCode["NO_FACES_FOUND"] = "NO_FACES_FOUND";
        StatusCode["MORE_THAN_ONE_FACE_FOUND"] = "MORE_THAN_ONE_FACE_FOUND";
        StatusCode["FACE_NOT_CENTERED"] = "FACE_NOT_CENTERED";
        StatusCode["FACE_TOO_BIG_OR_TOO_SMALL"] = "FACE_TOO_BIG_OR_TOO_SMALL";
        StatusCode["IMAGE_TOO_DARK"] = "IMAGE_TOO_DARK";
        StatusCode["IMAGE_TOO_BRIGHT"] = "IMAGE_TOO_BRIGHT";
        StatusCode["CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING"] = "CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING";
        StatusCode["PROCESSING_NOT_STARTED"] = "PROCESSING_NOT_STARTED";
    })(exports.StatusCode || (exports.StatusCode = {}));
    // Strongly-typed error codes enum
    exports.ErrorCode = void 0;
    (function (ErrorCode) {
        ErrorCode["WASM_LOAD_FAILED"] = "WASM_LOAD_FAILED";
        ErrorCode["CAMERA_ACCESS_DENIED"] = "CAMERA_ACCESS_DENIED";
        ErrorCode["INVALID_API_KEY"] = "INVALID_API_KEY";
        ErrorCode["NETWORK_ERROR"] = "NETWORK_ERROR";
        ErrorCode["PROCESSING_FAILED"] = "PROCESSING_FAILED";
        ErrorCode["MEMORY_ERROR"] = "MEMORY_ERROR";
        ErrorCode["INVALID_CONFIG"] = "INVALID_CONFIG";
        ErrorCode["NOT_INITIALIZED"] = "NOT_INITIALIZED";
        ErrorCode["ALREADY_RUNNING"] = "ALREADY_RUNNING";
        ErrorCode["NOT_RUNNING"] = "NOT_RUNNING";
        ErrorCode["UNSUPPORTED_BROWSER"] = "UNSUPPORTED_BROWSER";
    })(exports.ErrorCode || (exports.ErrorCode = {}));
    // Comprehensive error class with proper typing
    class SmartSpectraError extends Error {
        constructor(message, code, category, recoverable = false, cause) {
            super(message);
            this.code = code;
            this.category = category;
            this.recoverable = recoverable;
            this.cause = cause;
            this.name = 'SmartSpectraError';
            // Set the cause if provided (for newer browsers)
            if (cause && 'cause' in Error.prototype) {
                this.cause = cause;
            }
        }
    }

    /**
     * Manages video capture and frame extraction for the SmartSpectra SDK
     * Handles getUserMedia, canvas operations, and RGB pixel data extraction
     */
    class VideoCaptureManager {
        constructor() {
            this.videoElement = null;
            this.stream = null;
            this.animationFrameId = null;
            this.isCapturing = false;
            this.canvas = document.createElement('canvas');
            const context = this.canvas.getContext('2d');
            if (!context) {
                throw new SmartSpectraError('Failed to get 2D canvas context', exports.ErrorCode.PROCESSING_FAILED, 'initialization');
            }
            this.context = context;
        }
        /**
         * Request camera permission and get media stream
         */
        async requestCameraPermission(constraints) {
            try {
                const defaultConstraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        frameRate: { ideal: 30 },
                        facingMode: 'user',
                        ...constraints
                    },
                    audio: false
                };
                this.stream = await navigator.mediaDevices.getUserMedia(defaultConstraints);
                return this.stream;
            }
            catch (error) {
                if (error instanceof Error) {
                    if (error.name === 'NotAllowedError') {
                        throw new SmartSpectraError('Camera access denied by user', exports.ErrorCode.CAMERA_ACCESS_DENIED, 'runtime', false, error);
                    }
                    else if (error.name === 'NotFoundError') {
                        throw new SmartSpectraError('No camera found', exports.ErrorCode.CAMERA_ACCESS_DENIED, 'runtime', false, error);
                    }
                }
                throw new SmartSpectraError('Failed to access camera', exports.ErrorCode.CAMERA_ACCESS_DENIED, 'runtime', false, error instanceof Error ? error : new Error(String(error)));
            }
        }
        /**
         * Initialize with a video element
         */
        async setVideoElement(videoElement) {
            this.videoElement = videoElement;
            return new Promise((resolve, reject) => {
                const onLoadedMetadata = () => {
                    this.updateCanvasSize();
                    videoElement.removeEventListener('loadedmetadata', onLoadedMetadata);
                    resolve();
                };
                const onError = (error) => {
                    videoElement.removeEventListener('error', onError);
                    reject(new SmartSpectraError('Video element failed to load', exports.ErrorCode.PROCESSING_FAILED, 'runtime', false));
                };
                videoElement.addEventListener('loadedmetadata', onLoadedMetadata);
                videoElement.addEventListener('error', onError);
                // If metadata is already loaded
                if (videoElement.readyState >= 1) {
                    onLoadedMetadata();
                }
            });
        }
        /**
         * Set video stream
         */
        async setVideoStream(stream) {
            this.stream = stream;
            if (!this.videoElement) {
                this.videoElement = document.createElement('video');
                this.videoElement.autoplay = true;
                this.videoElement.muted = true;
                this.videoElement.playsInline = true;
            }
            this.videoElement.srcObject = stream;
            await this.setVideoElement(this.videoElement);
        }
        /**
         * Start capturing frames
         */
        startCapture() {
            if (this.isCapturing) {
                return;
            }
            this.isCapturing = true;
            this.captureLoop();
        }
        /**
         * Stop capturing frames
         */
        stopCapture() {
            this.isCapturing = false;
            if (this.animationFrameId !== null) {
                cancelAnimationFrame(this.animationFrameId);
                this.animationFrameId = null;
            }
        }
        /**
         * Extract RGB pixel data from current video frame
         */
        extractPixelData() {
            if (!this.videoElement) {
                throw new SmartSpectraError('No video element available', exports.ErrorCode.NOT_INITIALIZED, 'runtime');
            }
            if (this.videoElement.readyState < 2) {
                throw new SmartSpectraError('Video not ready', exports.ErrorCode.PROCESSING_FAILED, 'runtime');
            }
            // Draw video frame to canvas
            this.context.drawImage(this.videoElement, 0, 0, this.canvas.width, this.canvas.height);
            // Get image data (RGBA format)
            const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const rgbaData = imageData.data;
            // Convert RGBA to RGB
            const rgbData = new Uint8Array(this.canvas.width * this.canvas.height * 3);
            for (let i = 0, j = 0; i < rgbaData.length; i += 4, j += 3) {
                rgbData[j] = rgbaData[i]; // R
                rgbData[j + 1] = rgbaData[i + 1]; // G
                rgbData[j + 2] = rgbaData[i + 2]; // B
                // Skip alpha channel
            }
            return {
                pixelData: rgbData,
                width: this.canvas.width,
                height: this.canvas.height,
                timestamp: performance.now(),
                format: 'RGB'
            };
        }
        /**
         * Get frame dimensions
         */
        getFrameDimensions() {
            return {
                width: this.canvas.width,
                height: this.canvas.height
            };
        }
        /**
         * Get current timestamp
         */
        getCurrentTimestamp() {
            return performance.now();
        }
        /**
         * Cleanup resources
         */
        cleanup() {
            this.stopCapture();
            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
                this.stream = null;
            }
            if (this.videoElement) {
                this.videoElement.srcObject = null;
                this.videoElement = null;
            }
        }
        /**
         * Check if browser supports required features
         */
        static checkBrowserSupport() {
            const missing = [];
            if (!navigator.mediaDevices?.getUserMedia) {
                missing.push('getUserMedia');
            }
            if (!HTMLCanvasElement.prototype.getContext) {
                missing.push('Canvas 2D context');
            }
            if (!window.performance?.now) {
                missing.push('Performance API');
            }
            return {
                supported: missing.length === 0,
                missing
            };
        }
        updateCanvasSize() {
            if (this.videoElement) {
                this.canvas.width = this.videoElement.videoWidth;
                this.canvas.height = this.videoElement.videoHeight;
            }
        }
        captureLoop() {
            if (!this.isCapturing) {
                return;
            }
            // This method can be extended to automatically capture frames
            // For now, frames are captured on-demand via extractPixelData()
            this.animationFrameId = requestAnimationFrame(() => this.captureLoop());
        }
    }

    /**
     * Network client for SmartSpectra API communication
     * Handles REST API calls with API key authentication
     */
    class NetworkClient {
        constructor() {
            this.apiKey = '';
            this.baseUrl = 'https://physiology.presagetech.com/api';
            this.timeout = 30000; // 30 seconds
        }
        /**
         * Set API key for authentication
         */
        setApiKey(apiKey) {
            this.apiKey = apiKey;
        }
        /**
         * Set base URL for API calls
         */
        setBaseUrl(baseUrl) {
            this.baseUrl = baseUrl;
        }
        /**
         * Set request timeout
         */
        setTimeout(timeout) {
            this.timeout = timeout;
        }
        /**
         * Validate API key format
         */
        validateApiKey() {
            if (!this.apiKey) {
                return false;
            }
            // Basic validation - API key should be non-empty string
            // Add more specific validation based on actual API key format
            return this.apiKey.length > 0 && typeof this.apiKey === 'string';
        }
        /**
         * Send metrics request to the API
         */
        async sendMetricsRequest(data) {
            if (!this.validateApiKey()) {
                throw new SmartSpectraError('Invalid or missing API key', exports.ErrorCode.INVALID_API_KEY, 'configuration');
            }
            const url = `${this.baseUrl}/metrics`;
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`,
                        'User-Agent': 'SmartSpectra-WASM-JS-SDK/1.0.0'
                    },
                    body: JSON.stringify(data),
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                if (!response.ok) {
                    await this.handleErrorResponse(response);
                }
                const result = await response.json();
                return result;
            }
            catch (error) {
                if (error instanceof SmartSpectraError) {
                    throw error;
                }
                if (error instanceof Error) {
                    if (error.name === 'AbortError') {
                        throw new SmartSpectraError('Request timeout', exports.ErrorCode.NETWORK_ERROR, 'runtime', true, error);
                    }
                    if (error.name === 'TypeError' && error.message.includes('fetch')) {
                        throw new SmartSpectraError('Network connection failed', exports.ErrorCode.NETWORK_ERROR, 'runtime', true, error);
                    }
                }
                throw new SmartSpectraError('Network request failed', exports.ErrorCode.NETWORK_ERROR, 'runtime', true, error instanceof Error ? error : new Error(String(error)));
            }
        }
        /**
         * Test API connectivity and authentication
         */
        async testConnection() {
            if (!this.validateApiKey()) {
                throw new SmartSpectraError('Invalid or missing API key', exports.ErrorCode.INVALID_API_KEY, 'configuration');
            }
            const url = `${this.baseUrl}/health`;
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout for health check
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'User-Agent': 'SmartSpectra-WASM-JS-SDK/1.0.0'
                    },
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                return response.ok;
            }
            catch (error) {
                return false;
            }
        }
        /**
         * Handle CORS and security requirements
         */
        handleCorsAndSecurity() {
            // Check if we're running in a secure context (HTTPS or localhost)
            if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                console.warn('SmartSpectra SDK: HTTPS is recommended for production use');
            }
            // Check for CORS support
            if (!window.fetch) {
                throw new SmartSpectraError('Fetch API not supported', exports.ErrorCode.UNSUPPORTED_BROWSER, 'initialization');
            }
        }
        /**
         * Get API status and version information
         */
        async getApiInfo() {
            const url = `${this.baseUrl}/info`;
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'SmartSpectra-WASM-JS-SDK/1.0.0'
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return await response.json();
            }
            catch (error) {
                throw new SmartSpectraError('Failed to get API information', exports.ErrorCode.NETWORK_ERROR, 'runtime', true, error instanceof Error ? error : new Error(String(error)));
            }
        }
        /**
         * Handle error responses from the API
         */
        async handleErrorResponse(response) {
            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
            let errorCode = exports.ErrorCode.NETWORK_ERROR;
            try {
                const errorData = await response.json();
                if (errorData.message) {
                    errorMessage = errorData.message;
                }
                if (errorData.code) {
                    errorMessage += ` (${errorData.code})`;
                }
            }
            catch {
                // If we can't parse the error response, use the default message
            }
            // Map HTTP status codes to specific error codes
            switch (response.status) {
                case 401:
                case 403:
                    errorCode = exports.ErrorCode.INVALID_API_KEY;
                    break;
                case 429:
                    errorMessage = 'Rate limit exceeded';
                    errorCode = exports.ErrorCode.NETWORK_ERROR;
                    break;
                case 500:
                case 502:
                case 503:
                case 504:
                    errorMessage = 'Server error';
                    errorCode = exports.ErrorCode.NETWORK_ERROR;
                    break;
                default:
                    errorCode = exports.ErrorCode.NETWORK_ERROR;
            }
            throw new SmartSpectraError(errorMessage, errorCode, 'runtime', response.status >= 500 // Server errors are potentially recoverable
            );
        }
        /**
         * Check if the current environment supports the required network features
         */
        static checkNetworkSupport() {
            const missing = [];
            if (!window.fetch) {
                missing.push('Fetch API');
            }
            if (!window.AbortController) {
                missing.push('AbortController');
            }
            if (!window.URL) {
                missing.push('URL API');
            }
            return {
                supported: missing.length === 0,
                missing
            };
        }
    }

    /**
     * Main SmartSpectra Web SDK class
     * Provides a high-level JavaScript API for heart rate and respiration measurement
     */
    let SmartSpectraWebSDK$2 = class SmartSpectraWebSDK {
        constructor() {
            this.wasmInstance = null;
            this.config = null;
            this.currentMode = null;
            this.isRunning = false;
            this.errorHistory = [];
            this.errorListeners = [];
            this.metricsBuffer = null;
            this.meshPoints = [];
            this.denseMeshPoints = [];
            this.videoCaptureManager = new VideoCaptureManager();
            this.networkClient = new NetworkClient();
        }
        /**
         * Static initialization - loads WASM module
         */
        static async initialize(options) {
            if (SmartSpectraWebSDK.initialized) {
                return;
            }
            try {
                // Check browser support
                const support = SmartSpectraWebSDK.getBrowserSupport();
                if (!support.overall) {
                    throw new SmartSpectraError(`Browser not supported. Missing: ${support.missing.join(', ')}`, exports.ErrorCode.UNSUPPORTED_BROWSER, 'initialization');
                }
                // Load WASM module
                const wasmPath = options?.wasmPath || './wasm/smartspectra_wasm.js';
                // Dynamic import of the WASM module
                const wasmModuleFactory = await import(wasmPath);
                SmartSpectraWebSDK.wasmModule = await wasmModuleFactory.default();
                SmartSpectraWebSDK.initialized = true;
            }
            catch (error) {
                throw new SmartSpectraError('Failed to initialize WASM module', exports.ErrorCode.WASM_LOAD_FAILED, 'initialization', false, error instanceof Error ? error : new Error(String(error)));
            }
        }
        /**
         * Get singleton instance
         */
        static getInstance() {
            if (!SmartSpectraWebSDK.initialized) {
                throw new SmartSpectraError('SDK not initialized. Call SmartSpectraWebSDK.initialize() first', exports.ErrorCode.NOT_INITIALIZED, 'initialization');
            }
            if (!SmartSpectraWebSDK.instance) {
                SmartSpectraWebSDK.instance = new SmartSpectraWebSDK();
            }
            return SmartSpectraWebSDK.instance;
        }
        /**
         * Get SDK version
         */
        static getVersion() {
            return '1.0.0'; // TODO: Get from package.json
        }
        /**
         * Check if browser is supported
         */
        static isSupported() {
            return SmartSpectraWebSDK.getBrowserSupport().overall;
        }
        /**
         * Get detailed browser support information
         */
        static getBrowserSupport() {
            const missing = [];
            // Check WebAssembly support
            if (!window.WebAssembly) {
                missing.push('WebAssembly');
            }
            // Check video capture support
            const videoSupport = VideoCaptureManager.checkBrowserSupport();
            if (!videoSupport.supported) {
                missing.push(...videoSupport.missing);
            }
            // Check network support
            const networkSupport = NetworkClient.checkNetworkSupport();
            if (!networkSupport.supported) {
                missing.push(...networkSupport.missing);
            }
            // Check other required APIs
            if (!window.performance?.now) {
                missing.push('Performance API');
            }
            if (!window.requestAnimationFrame) {
                missing.push('RequestAnimationFrame');
            }
            return {
                webAssembly: window.WebAssembly !== undefined,
                getUserMedia: !!navigator.mediaDevices?.getUserMedia,
                canvas: !!HTMLCanvasElement.prototype.getContext,
                fetch: !!window.fetch,
                webGL: !!window.WebGLRenderingContext,
                sharedArrayBuffer: !!window.SharedArrayBuffer,
                overall: missing.length === 0,
                missing
            };
        }
        /**
         * Unload the SDK and cleanup resources
         */
        static unload() {
            if (SmartSpectraWebSDK.instance) {
                SmartSpectraWebSDK.instance.dispose();
                SmartSpectraWebSDK.instance = null;
            }
            SmartSpectraWebSDK.wasmModule = null;
            SmartSpectraWebSDK.initialized = false;
        }
        /**
         * Configure the SDK
         */
        configure(config) {
            this.config = { ...config };
            // Configure network client
            if (config.apiKey) {
                this.networkClient.setApiKey(config.apiKey);
            }
            // Initialize WASM instance if not already done
            if (!this.wasmInstance) {
                this.initializeWASMInstance();
            }
            // Configure WASM instance
            this.configureWASMInstance();
        }
        /**
         * Individual setter methods (like Swift/Android)
         */
        setApiKey(apiKey) {
            if (!this.config) {
                this.config = { mode: exports.SmartSpectraMode.SPOT };
            }
            this.config.apiKey = apiKey;
            this.networkClient.setApiKey(apiKey);
        }
        setMeasurementDuration(duration) {
            if (!this.config) {
                this.config = { mode: exports.SmartSpectraMode.SPOT };
            }
            // Clamp duration to valid range (20-120 seconds)
            this.config.spotDuration = Math.max(20, Math.min(120, duration));
        }
        setSmartSpectraMode(mode) {
            if (!this.config) {
                this.config = { mode };
            }
            else {
                this.config.mode = mode;
            }
        }
        setCameraPosition(position) {
            if (!this.config) {
                this.config = { mode: exports.SmartSpectraMode.SPOT };
            }
            this.config.cameraPosition = position;
        }
        /**
         * Video handling methods
         */
        async setVideoElement(videoElement) {
            await this.videoCaptureManager.setVideoElement(videoElement);
        }
        async setVideoStream(stream) {
            await this.videoCaptureManager.setVideoStream(stream);
        }
        /**
         * Request camera permission
         */
        async requestCameraPermission() {
            const constraints = this.config?.videoConstraints || {};
            if (this.config?.cameraPosition) {
                constraints.facingMode = this.config.cameraPosition === 'front' ? 'user' : 'environment';
            }
            return await this.videoCaptureManager.requestCameraPermission(constraints);
        }
        /**
         * Start measurement
         */
        async start(options) {
            if (this.isRunning) {
                throw new SmartSpectraError('Already running', exports.ErrorCode.ALREADY_RUNNING, 'runtime');
            }
            if (!this.wasmInstance) {
                throw new SmartSpectraError('WASM instance not initialized', exports.ErrorCode.NOT_INITIALIZED, 'runtime');
            }
            try {
                this.currentMode = options.mode;
                this.isRunning = true;
                // Setup callbacks
                this.setupWASMCallbacks(options);
                // Start appropriate mode
                let success = false;
                if (options.mode === exports.SmartSpectraMode.SPOT) {
                    const duration = options.duration || this.config?.spotDuration || 30;
                    success = this.wasmInstance.startSpotMode(duration);
                }
                else {
                    success = this.wasmInstance.startContinuousMode();
                }
                if (!success) {
                    this.isRunning = false;
                    throw new SmartSpectraError(this.wasmInstance.getLastError() || 'Failed to start measurement', exports.ErrorCode.PROCESSING_FAILED, 'runtime');
                }
                // Start video capture and processing loop
                this.startProcessingLoop();
                // For spot mode, return a promise that resolves when complete
                if (options.mode === exports.SmartSpectraMode.SPOT) {
                    return new Promise((resolve, reject) => {
                        const originalOnResult = options.onResult;
                        options.onResult = (result) => {
                            if (originalOnResult)
                                originalOnResult(result);
                            resolve(result);
                        };
                        const originalOnError = options.onError;
                        options.onError = (error) => {
                            if (originalOnError)
                                originalOnError(error);
                            reject(error);
                        };
                    });
                }
            }
            catch (error) {
                this.isRunning = false;
                throw error;
            }
        }
        /**
         * Stop measurement
         */
        async stop() {
            if (!this.isRunning) {
                return;
            }
            try {
                if (this.wasmInstance) {
                    this.wasmInstance.stop();
                }
                this.videoCaptureManager.stopCapture();
                this.isRunning = false;
                this.currentMode = null;
            }
            catch (error) {
                throw new SmartSpectraError('Failed to stop measurement', exports.ErrorCode.PROCESSING_FAILED, 'runtime', false, error instanceof Error ? error : new Error(String(error)));
            }
        }
        /**
         * State management methods
         */
        getCurrentMode() {
            return this.currentMode;
        }
        getModeProgress() {
            // TODO: Implement progress tracking
            return this.currentMode === exports.SmartSpectraMode.CONTINUOUS ? -1 : 0;
        }
        getModeState() {
            return {
                mode: this.currentMode || exports.SmartSpectraMode.SPOT,
                progress: this.getModeProgress(),
                frameCount: 0, // TODO: Track frame count
                duration: 0, // TODO: Track duration
                quality: 'good', // TODO: Implement quality assessment
                status: this.wasmInstance ? this.getStatusFromCode(this.wasmInstance.getCurrentStatus()) : exports.StatusCode.PROCESSING_NOT_STARTED,
                isStable: false // TODO: Implement stability tracking
            };
        }
        /**
         * Property-based access (like Swift/Android)
         */
        get metricsBuffer() {
            return this.metricsBuffer;
        }
        get meshPoints() {
            return this.meshPoints;
        }
        get denseMeshPoints() {
            return this.denseMeshPoints;
        }
        /**
         * Utility methods
         */
        isInitialized() {
            return this.wasmInstance !== null;
        }
        isRunning() {
            return this.isRunning;
        }
        getVersion() {
            return SmartSpectraWebSDK.getVersion();
        }
        /**
         * Error handling methods
         */
        getLastError() {
            return this.wasmInstance ? this.wasmInstance.getLastError() : '';
        }
        addErrorListener(listener) {
            this.errorListeners.push(listener);
        }
        removeErrorListener(listener) {
            const index = this.errorListeners.indexOf(listener);
            if (index > -1) {
                this.errorListeners.splice(index, 1);
            }
        }
        getErrorHistory() {
            return [...this.errorHistory];
        }
        clearErrorHistory() {
            this.errorHistory = [];
        }
        /**
         * Cleanup and disposal
         */
        async dispose() {
            await this.stop();
            this.videoCaptureManager.cleanup();
            if (this.wasmInstance) {
                this.wasmInstance.cleanup();
                this.wasmInstance = null;
            }
            this.config = null;
            this.metricsBuffer = null;
            this.meshPoints = [];
            this.denseMeshPoints = [];
            this.errorHistory = [];
            this.errorListeners = [];
        }
        /**
         * Private methods
         */
        initializeWASMInstance() {
            if (!SmartSpectraWebSDK.wasmModule) {
                throw new SmartSpectraError('WASM module not loaded', exports.ErrorCode.NOT_INITIALIZED, 'initialization');
            }
            this.wasmInstance = new SmartSpectraWebSDK.wasmModule.SmartSpectraWASM();
        }
        configureWASMInstance() {
            if (!this.wasmInstance || !this.config) {
                return;
            }
            const wasmConfig = {
                mode: this.config.mode,
                api_key: this.config.apiKey || '',
                spot_duration_s: this.config.spotDuration,
                buffer_duration_s: this.config.bufferDuration,
                scale_input: this.config.scaleInput,
                enable_phasic_bp: this.config.enablePhasicBP,
                enable_dense_facemesh_points: this.config.enableDenseFacemeshPoints,
                use_full_range_face_detection: this.config.useFullRangeFaceDetection,
                enable_edge_metrics: this.config.enableEdgeMetrics,
                verbosity_level: this.config.verbosityLevel
            };
            const success = this.wasmInstance.initialize(JSON.stringify(wasmConfig));
            if (!success) {
                throw new SmartSpectraError(this.wasmInstance.getLastError() || 'WASM configuration failed', exports.ErrorCode.INVALID_CONFIG, 'configuration');
            }
        }
        setupWASMCallbacks(options) {
            if (!this.wasmInstance)
                return;
            // Metrics callback
            this.wasmInstance.setOnMetricsCallback((metricsJson, timestamp) => {
                try {
                    const metrics = JSON.parse(metricsJson);
                    this.metricsBuffer = metrics;
                    if (options.onResult) {
                        options.onResult(metrics);
                    }
                }
                catch (error) {
                    this.handleError(new SmartSpectraError('Failed to parse metrics', exports.ErrorCode.PROCESSING_FAILED, 'runtime', false, error instanceof Error ? error : new Error(String(error))));
                }
            });
            // Status callback
            this.wasmInstance.setOnStatusCallback((statusCode) => {
                const status = this.getStatusFromCode(statusCode);
                if (options.onStatus) {
                    options.onStatus(status);
                }
            });
            // Error callback
            this.wasmInstance.setOnErrorCallback((errorMessage) => {
                const error = new SmartSpectraError(errorMessage, exports.ErrorCode.PROCESSING_FAILED, 'runtime');
                this.handleError(error);
                if (options.onError) {
                    options.onError(error);
                }
            });
        }
        startProcessingLoop() {
            this.videoCaptureManager.startCapture();
            this.processFrameLoop();
        }
        processFrameLoop() {
            if (!this.isRunning || !this.wasmInstance) {
                return;
            }
            try {
                const frame = this.videoCaptureManager.extractPixelData();
                const success = this.wasmInstance.processFrame(frame.pixelData, frame.width, frame.height, frame.timestamp);
                if (!success) {
                    const error = this.wasmInstance.getLastError();
                    if (error) {
                        this.handleError(new SmartSpectraError(error, exports.ErrorCode.PROCESSING_FAILED, 'runtime'));
                    }
                }
            }
            catch (error) {
                this.handleError(new SmartSpectraError('Frame processing failed', exports.ErrorCode.PROCESSING_FAILED, 'runtime', true, error instanceof Error ? error : new Error(String(error))));
            }
            // Continue processing loop
            if (this.isRunning) {
                requestAnimationFrame(() => this.processFrameLoop());
            }
        }
        getStatusFromCode(code) {
            const statusMap = {
                0: exports.StatusCode.OK,
                1: exports.StatusCode.NO_FACES_FOUND,
                2: exports.StatusCode.MORE_THAN_ONE_FACE_FOUND,
                3: exports.StatusCode.FACE_NOT_CENTERED,
                4: exports.StatusCode.FACE_TOO_BIG_OR_TOO_SMALL,
                5: exports.StatusCode.IMAGE_TOO_DARK,
                6: exports.StatusCode.IMAGE_TOO_BRIGHT,
                7: exports.StatusCode.CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING,
                8: exports.StatusCode.PROCESSING_NOT_STARTED
            };
            return statusMap[code] || exports.StatusCode.PROCESSING_NOT_STARTED;
        }
        handleError(error) {
            const report = {
                error,
                timestamp: Date.now(),
                context: {
                    mode: this.currentMode,
                    isRunning: this.isRunning,
                    isInitialized: this.isInitialized()
                },
                recovery: {
                    canRecover: error.recoverable,
                    recoveryAction: error.recoverable ? 'Retry operation' : 'Restart SDK',
                    attempts: 0
                }
            };
            this.errorHistory.push(report);
            // Limit error history size
            if (this.errorHistory.length > 100) {
                this.errorHistory.shift();
            }
            // Notify error listeners
            this.errorListeners.forEach(listener => {
                try {
                    listener(report);
                }
                catch (listenerError) {
                    console.error('Error in error listener:', listenerError);
                }
            });
        }
    };
    SmartSpectraWebSDK$2.instance = null;
    SmartSpectraWebSDK$2.wasmModule = null;
    SmartSpectraWebSDK$2.initialized = false;

    /**
     * SmartSpectra Web SDK
     *
     * A WebAssembly-based JavaScript SDK for integrating SmartSpectra's
     * heart rate and respiration rate measurement capabilities into web applications.
     *
     * @version 1.0.0
     * <AUTHOR> Technologies
     */
    // Main SDK class
    // Default export for convenience
    var SmartSpectraWebSDK$1 = SmartSpectraWebSDK;

    exports.NetworkClient = NetworkClient;
    exports.SmartSpectraError = SmartSpectraError;
    exports.SmartSpectraWebSDK = SmartSpectraWebSDK$2;
    exports.VideoCaptureManager = VideoCaptureManager;
    exports.default = SmartSpectraWebSDK$1;

    Object.defineProperty(exports, '__esModule', { value: true });

}));
//# sourceMappingURL=index.umd.js.map
