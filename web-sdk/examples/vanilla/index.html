<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartSpectra Vanilla JS Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .api-key-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            margin: 10px 0;
        }

        .video-container {
            position: relative;
            text-align: center;
        }

        #videoElement {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
        }

        .status-ok { background: #28a745; color: white; }
        .status-warning { background: #ffc107; color: black; }
        .status-error { background: #dc3545; color: white; }

        .metrics-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }

        .metric-label {
            color: #666;
            margin-top: 5px;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #666;
        }

        select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        label {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>SmartSpectra Vanilla JS Example</h1>
    <p style="text-align: center; color: #666;">Heart Rate and Breathing Rate Measurement</p>

    <!-- Configuration -->
    <div class="container">
        <h3>Configuration</h3>
        <input
            type="text"
            id="apiKeyInput"
            class="api-key-input"
            placeholder="Enter your API key (YOUR_API_KEY)"
            value="YOUR_API_KEY"
        >

        <div class="controls">
            <label>
                Mode:
                <select id="modeSelect">
                    <option value="spot">Spot (Fixed Duration)</option>
                    <option value="continuous">Continuous</option>
                </select>
            </label>

            <label id="durationLabel">
                Duration:
                <select id="durationSelect">
                    <option value="20">20 seconds</option>
                    <option value="30" selected>30 seconds</option>
                    <option value="60">60 seconds</option>
                    <option value="120">120 seconds</option>
                </select>
            </label>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="errorContainer" class="error hidden"></div>
    <div id="successContainer" class="success hidden"></div>

    <!-- Camera Feed -->
    <div class="container">
        <h3>Camera Feed</h3>
        <div class="video-container">
            <video id="videoElement" autoplay muted playsinline></video>
            <div id="statusIndicator" class="status-indicator status-error">Ready to start</div>
        </div>

        <div class="controls">
            <button id="enableCameraBtn">Enable Camera</button>
            <button id="startMeasurementBtn" disabled>Start Measurement</button>
            <button id="stopMeasurementBtn" disabled>Stop Measurement</button>
        </div>

        <!-- Progress Bar -->
        <div id="progressContainer" class="progress-container hidden">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="progressText" class="progress-text">0% Complete</div>
        </div>
    </div>

    <!-- Vital Signs Display -->
    <div class="container">
        <h3>Live Measurements</h3>
        <div class="metrics-display">
            <div class="metric-card">
                <div id="heartRateValue" class="metric-value">--</div>
                <div class="metric-label">Heart Rate (BPM)</div>
            </div>

            <div class="metric-card">
                <div id="breathingRateValue" class="metric-value">--</div>
                <div class="metric-label">Breathing Rate (BPM)</div>
            </div>
        </div>

        <div id="confidenceDisplay" class="hidden" style="text-align: center; margin: 10px 0;">
            Confidence: <span id="confidenceValue">--</span>%
        </div>

        <div id="timestampDisplay" class="hidden" style="text-align: center; font-size: 0.9em; color: #666;">
            Last updated: <span id="timestampValue">--</span>
        </div>
    </div>

    <!-- Instructions -->
    <div class="container">
        <h3>Instructions</h3>
        <ol>
            <li>Enter your SmartSpectra API key above</li>
            <li>Click "Enable Camera" to access your webcam</li>
            <li>Position your face in the camera view</li>
            <li>Click "Start Measurement" to begin</li>
            <li>Keep still and look at the camera during measurement</li>
            <li>Your heart rate and breathing rate will appear in real-time</li>
        </ol>
    </div>

    <!-- Load SDK -->
    <script src="smartspectra-web-sdk.js"></script>
    <script>
        // Use UMD build - SDK is available as global SmartSpectraWebSDK
        const { SmartSpectraWebSDK, SmartSpectraMode, StatusCode } = window.SmartSpectraWebSDK;

        // Global variables
        let sdk = null;
        let isInitialized = false;
        let isRunning = false;
        let stream = null;
        let currentMode = SmartSpectraMode.SPOT;
        let progressInterval = null;

        // DOM elements
        const apiKeyInput = document.getElementById('apiKeyInput');
        const modeSelect = document.getElementById('modeSelect');
        const durationSelect = document.getElementById('durationSelect');
        const durationLabel = document.getElementById('durationLabel');
        const enableCameraBtn = document.getElementById('enableCameraBtn');
        const startMeasurementBtn = document.getElementById('startMeasurementBtn');
        const stopMeasurementBtn = document.getElementById('stopMeasurementBtn');
        const videoElement = document.getElementById('videoElement');
        const statusIndicator = document.getElementById('statusIndicator');
        const errorContainer = document.getElementById('errorContainer');
        const successContainer = document.getElementById('successContainer');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const heartRateValue = document.getElementById('heartRateValue');
        const breathingRateValue = document.getElementById('breathingRateValue');
        const confidenceDisplay = document.getElementById('confidenceDisplay');
        const confidenceValue = document.getElementById('confidenceValue');
        const timestampDisplay = document.getElementById('timestampDisplay');
        const timestampValue = document.getElementById('timestampValue');

        // Initialize SDK
        async function initializeSDK() {
            try {
                showMessage('Initializing SmartSpectra SDK...', 'success');

                await SmartSpectraWebSDK.initialize({
                    wasmPath: './wasm/smartspectra_wasm.js',
                    debug: true
                });

                sdk = SmartSpectraWebSDK.getInstance();
                isInitialized = true;

                showMessage('SDK initialized successfully!', 'success');
                updateUI();

            } catch (error) {
                console.error('Failed to initialize SDK:', error);
                showMessage(`Failed to initialize SDK: ${error.message}`, 'error');
            }
        }

        // Show message
        function showMessage(message, type) {
            hideMessages();

            if (type === 'error') {
                errorContainer.textContent = message;
                errorContainer.classList.remove('hidden');
            } else {
                successContainer.textContent = message;
                successContainer.classList.remove('hidden');
            }

            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(() => {
                    successContainer.classList.add('hidden');
                }, 3000);
            }
        }

        // Hide messages
        function hideMessages() {
            errorContainer.classList.add('hidden');
            successContainer.classList.add('hidden');
        }

        // Update UI state
        function updateUI() {
            const hasCamera = !!stream;
            const hasApiKey = apiKeyInput.value && apiKeyInput.value !== 'YOUR_API_KEY';

            enableCameraBtn.disabled = !isInitialized || hasCamera;
            startMeasurementBtn.disabled = !isInitialized || !hasCamera || !hasApiKey || isRunning;
            stopMeasurementBtn.disabled = !isRunning;
            modeSelect.disabled = isRunning;
            durationSelect.disabled = isRunning;

            // Show/hide duration selector based on mode
            if (currentMode === SmartSpectraMode.CONTINUOUS) {
                durationLabel.classList.add('hidden');
            } else {
                durationLabel.classList.remove('hidden');
            }
        }

        // Configure SDK
        function configureSDK() {
            if (!sdk || !apiKeyInput.value || apiKeyInput.value === 'YOUR_API_KEY') {
                return;
            }

            try {
                sdk.configure({
                    apiKey: apiKeyInput.value,
                    mode: currentMode,
                    spotDuration: parseInt(durationSelect.value),
                    cameraPosition: 'front',
                    debug: true
                });

                hideMessages();
            } catch (error) {
                console.error('Failed to configure SDK:', error);
                showMessage(`Configuration failed: ${error.message}`, 'error');
            }
        }

        // Enable camera
        async function enableCamera() {
            if (!sdk) return;

            try {
                stream = await sdk.requestCameraPermission();
                videoElement.srcObject = stream;
                await sdk.setVideoElement(videoElement);

                showMessage('Camera enabled successfully!', 'success');
                updateUI();

            } catch (error) {
                console.error('Failed to enable camera:', error);
                showMessage(`Camera access failed: ${error.message}`, 'error');
            }
        }

        // Start measurement
        async function startMeasurement() {
            if (!sdk || !stream) return;

            try {
                isRunning = true;
                updateUI();
                hideMessages();

                const duration = parseInt(durationSelect.value);

                await sdk.start({
                    mode: currentMode,
                    duration: currentMode === SmartSpectraMode.SPOT ? duration : undefined,
                    onResult: (metrics) => {
                        console.log('Received metrics:', metrics);
                        updateVitalSigns(metrics);
                    },
                    onStatus: (status) => {
                        console.log('Status update:', status);
                        updateStatus(status);
                    },
                    onError: (error) => {
                        console.error('Measurement error:', error);
                        showMessage(`Measurement error: ${error.message}`, 'error');
                        stopMeasurement();
                    }
                });

                // Start progress tracking for spot mode
                if (currentMode === SmartSpectraMode.SPOT) {
                    startProgressTracking(duration);
                }

                showMessage('Measurement started!', 'success');

            } catch (error) {
                console.error('Failed to start measurement:', error);
                showMessage(`Failed to start measurement: ${error.message}`, 'error');
                isRunning = false;
                updateUI();
            }
        }

        // Stop measurement
        async function stopMeasurement() {
            if (!sdk) return;

            try {
                await sdk.stop();
                isRunning = false;

                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }

                progressContainer.classList.add('hidden');
                updateUI();
                updateStatus(StatusCode.PROCESSING_NOT_STARTED);

            } catch (error) {
                console.error('Failed to stop measurement:', error);
                showMessage(`Failed to stop measurement: ${error.message}`, 'error');
            }
        }

        // Update vital signs display
        function updateVitalSigns(metrics) {
            const heartRate = getLatestMeasurement(metrics.pulse?.rate);
            const breathingRate = getLatestMeasurement(metrics.breathing?.rate);
            const confidence = heartRate ? heartRate.confidence : null;

            heartRateValue.textContent = heartRate ? Math.round(heartRate.value) : '--';
            breathingRateValue.textContent = breathingRate ? Math.round(breathingRate.value) : '--';

            if (confidence !== null) {
                confidenceValue.textContent = Math.round(confidence * 100);
                confidenceDisplay.classList.remove('hidden');
            }

            timestampValue.textContent = new Date().toLocaleTimeString();
            timestampDisplay.classList.remove('hidden');
        }

        // Get latest measurement from array
        function getLatestMeasurement(measurements) {
            if (!measurements || measurements.length === 0) return null;
            return measurements[measurements.length - 1];
        }

        // Update status indicator
        function updateStatus(status) {
            const statusMessages = {
                [StatusCode.OK]: 'Good signal quality',
                [StatusCode.NO_FACES_FOUND]: 'No face detected',
                [StatusCode.MORE_THAN_ONE_FACE_FOUND]: 'Multiple faces detected',
                [StatusCode.FACE_NOT_CENTERED]: 'Please center your face',
                [StatusCode.FACE_TOO_BIG_OR_TOO_SMALL]: 'Adjust distance from camera',
                [StatusCode.IMAGE_TOO_DARK]: 'Increase lighting',
                [StatusCode.IMAGE_TOO_BRIGHT]: 'Reduce lighting',
                [StatusCode.PROCESSING_NOT_STARTED]: 'Ready to start'
            };

            const statusClasses = {
                [StatusCode.OK]: 'status-ok',
                [StatusCode.NO_FACES_FOUND]: 'status-warning',
                [StatusCode.MORE_THAN_ONE_FACE_FOUND]: 'status-warning',
                [StatusCode.FACE_NOT_CENTERED]: 'status-warning',
                [StatusCode.FACE_TOO_BIG_OR_TOO_SMALL]: 'status-warning',
                [StatusCode.IMAGE_TOO_DARK]: 'status-warning',
                [StatusCode.IMAGE_TOO_BRIGHT]: 'status-warning',
                [StatusCode.PROCESSING_NOT_STARTED]: 'status-error'
            };

            statusIndicator.textContent = statusMessages[status] || 'Unknown status';
            statusIndicator.className = `status-indicator ${statusClasses[status] || 'status-error'}`;
        }

        // Start progress tracking
        function startProgressTracking(duration) {
            progressContainer.classList.remove('hidden');
            const startTime = Date.now();
            const totalDuration = duration * 1000;

            progressInterval = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min((elapsed / totalDuration) * 100, 100);

                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${Math.round(progress)}% Complete`;

                if (progress >= 100) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
            }, 100);
        }

        // Event listeners
        apiKeyInput.addEventListener('input', () => {
            configureSDK();
            updateUI();
        });

        modeSelect.addEventListener('change', (e) => {
            currentMode = e.target.value;
            configureSDK();
            updateUI();
        });

        durationSelect.addEventListener('change', () => {
            configureSDK();
        });

        enableCameraBtn.addEventListener('click', enableCamera);
        startMeasurementBtn.addEventListener('click', startMeasurement);
        stopMeasurementBtn.addEventListener('click', stopMeasurement);

        // Initialize on page load
        window.addEventListener('load', () => {
            initializeSDK();
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            SmartSpectraWebSDK.unload();
        });
    </script>
</body>
</html>
