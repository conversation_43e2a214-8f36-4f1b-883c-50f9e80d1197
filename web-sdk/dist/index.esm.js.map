{"version": 3, "file": "index.esm.js", "sources": ["../src/js/types.ts", "../src/js/video-capture-manager.ts", "../src/js/network-client.ts", "../src/js/smartspectra-web-sdk.ts", "../src/js/index.ts"], "sourcesContent": [null, null, null, null, null], "names": ["SmartSpectraWebSDK"], "mappings": "AAAA;;;AAGG;AAEH;IACY;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,gBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AAC3B,CAAC,EAHW,gBAAgB,KAAhB,gBAAgB,GAAA,EAAA,CAAA,CAAA;AAQ5B;IACY;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,IAAA,CAAA,GAAA,IAAS;AACT,IAAA,UAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACjC,IAAA,UAAA,CAAA,0BAAA,CAAA,GAAA,0BAAqD;AACrD,IAAA,UAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC;AACvC,IAAA,UAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD;AACvD,IAAA,UAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACjC,IAAA,UAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC;AACrC,IAAA,UAAA,CAAA,qCAAA,CAAA,GAAA,qCAA2E;AAC3E,IAAA,UAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD;AACnD,CAAC,EAVW,UAAU,KAAV,UAAU,GAAA,EAAA,CAAA,CAAA;AA2ItB;IACY;AAAZ,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC;AACrC,IAAA,SAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C;AAC7C,IAAA,SAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,SAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AAC/B,IAAA,SAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC;AACvC,IAAA,SAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACjC,IAAA,SAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,SAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,SAAA,CAAA,aAAA,CAAA,GAAA,aAA2B;AAC3B,IAAA,SAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C;AAC7C,CAAC,EAZW,SAAS,KAAT,SAAS,GAAA,EAAA,CAAA,CAAA;AAcrB;AACM,MAAO,iBAAkB,SAAQ,KAAK,CAAA;IAC1C,WAAA,CACE,OAAe,EACR,IAAe,EACf,QAAuB,EACvB,WAAA,GAAuB,KAAK,EAC5B,KAAa,EAAA;QAEpB,KAAK,CAAC,OAAO,CAAC;QALP,IAAA,CAAA,IAAI,GAAJ,IAAI;QACJ,IAAA,CAAA,QAAQ,GAAR,QAAQ;QACR,IAAA,CAAA,WAAW,GAAX,WAAW;QACX,IAAA,CAAA,KAAK,GAAL,KAAK;AAGZ,QAAA,IAAI,CAAC,IAAI,GAAG,mBAAmB;;QAG/B,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,CAAC,SAAS,EAAE;AACtC,YAAA,IAAY,CAAC,KAAK,GAAG,KAAK;QAC7B;IACF;AACD;;ACxLD;;;AAGG;MACU,mBAAmB,CAAA;AAQ9B,IAAA,WAAA,GAAA;QALQ,IAAA,CAAA,YAAY,GAA4B,IAAI;QAC5C,IAAA,CAAA,MAAM,GAAuB,IAAI;QACjC,IAAA,CAAA,gBAAgB,GAAkB,IAAI;QACtC,IAAA,CAAA,WAAW,GAAG,KAAK;QAGzB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,iBAAiB,CACzB,iCAAiC,EACjC,SAAS,CAAC,iBAAiB,EAC3B,gBAAgB,CACjB;QACH;AACA,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;IACxB;AAEA;;AAEG;IACH,MAAM,uBAAuB,CAAC,WAAmC,EAAA;AAC/D,QAAA,IAAI;AACF,YAAA,MAAM,kBAAkB,GAA2B;AACjD,gBAAA,KAAK,EAAE;AACL,oBAAA,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;AACrB,oBAAA,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;AACtB,oBAAA,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;AACxB,oBAAA,UAAU,EAAE,MAAM;AAClB,oBAAA,GAAG;AACJ,iBAAA;AACD,gBAAA,KAAK,EAAE;aACR;AAED,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,kBAAkB,CAAC;YAC3E,OAAO,IAAI,CAAC,MAAM;QAEpB;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,IAAI,KAAK,YAAY,KAAK,EAAE;AAC1B,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE;AACpC,oBAAA,MAAM,IAAI,iBAAiB,CACzB,8BAA8B,EAC9B,SAAS,CAAC,oBAAoB,EAC9B,SAAS,EACT,KAAK,EACL,KAAK,CACN;gBACH;AAAO,qBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE;AACzC,oBAAA,MAAM,IAAI,iBAAiB,CACzB,iBAAiB,EACjB,SAAS,CAAC,oBAAoB,EAC9B,SAAS,EACT,KAAK,EACL,KAAK,CACN;gBACH;YACF;AAEA,YAAA,MAAM,IAAI,iBAAiB,CACzB,yBAAyB,EACzB,SAAS,CAAC,oBAAoB,EAC9B,SAAS,EACT,KAAK,EACL,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1D;QACH;IACF;AAEA;;AAEG;IACH,MAAM,eAAe,CAAC,YAA8B,EAAA;AAClD,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY;QAEhC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,MAAM,gBAAgB,GAAG,MAAW;gBAClC,IAAI,CAAC,gBAAgB,EAAE;AACvB,gBAAA,YAAY,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AACpE,gBAAA,OAAO,EAAE;AACX,YAAA,CAAC;AAED,YAAA,MAAM,OAAO,GAAG,CAAC,KAAY,KAAU;AACrC,gBAAA,YAAY,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;AAClD,gBAAA,MAAM,CAAC,IAAI,iBAAiB,CAC1B,8BAA8B,EAC9B,SAAS,CAAC,iBAAiB,EAC3B,SAAS,EACT,KAAK,CACN,CAAC;AACJ,YAAA,CAAC;AAED,YAAA,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AACjE,YAAA,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC;;AAG/C,YAAA,IAAI,YAAY,CAAC,UAAU,IAAI,CAAC,EAAE;AAChC,gBAAA,gBAAgB,EAAE;YACpB;AACF,QAAA,CAAC,CAAC;IACJ;AAEA;;AAEG;IACH,MAAM,cAAc,CAAC,MAAmB,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AAEpB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACnD,YAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI;AACjC,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI;AAC9B,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI;QACtC;AAEA,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,MAAM;QACpC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;IAC/C;AAEA;;AAEG;IACH,YAAY,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB;QACF;AAEA,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC,WAAW,EAAE;IACpB;AAEA;;AAEG;IACH,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AACxB,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;AAClC,YAAA,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAC3C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;QAC9B;IACF;AAEA;;AAEG;IACH,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,iBAAiB,CACzB,4BAA4B,EAC5B,SAAS,CAAC,eAAe,EACzB,SAAS,CACV;QACH;QAEA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,CAAC,EAAE;YACpC,MAAM,IAAI,iBAAiB,CACzB,iBAAiB,EACjB,SAAS,CAAC,iBAAiB,EAC3B,SAAS,CACV;QACH;;QAGA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;;QAGtF,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACxF,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI;;AAG/B,QAAA,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAC1D,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,YAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,YAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;QAEnC;QAEA,OAAO;AACL,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;AACxB,YAAA,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;AAC1B,YAAA,SAAS,EAAE,WAAW,CAAC,GAAG,EAAE;AAC5B,YAAA,MAAM,EAAE;SACT;IACH;AAEA;;AAEG;IACH,kBAAkB,GAAA;QAChB,OAAO;AACL,YAAA,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;AACxB,YAAA,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC;SACrB;IACH;AAEA;;AAEG;IACH,mBAAmB,GAAA;AACjB,QAAA,OAAO,WAAW,CAAC,GAAG,EAAE;IAC1B;AAEA;;AAEG;IACH,OAAO,GAAA;QACL,IAAI,CAAC,WAAW,EAAE;AAElB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;AACtD,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;QACpB;AAEA,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI;AAClC,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QAC1B;IACF;AAEA;;AAEG;AACH,IAAA,OAAO,mBAAmB,GAAA;QACxB,MAAM,OAAO,GAAa,EAAE;AAE5B,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,EAAE;AACzC,YAAA,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;QAC9B;AAEA,QAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,UAAU,EAAE;AAC3C,YAAA,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC;QACnC;AAEA,QAAA,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,GAAG,EAAE;AAC5B,YAAA,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;QACjC;QAEA,OAAO;AACL,YAAA,SAAS,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YAC/B;SACD;IACH;IAEQ,gBAAgB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU;YAChD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW;QACpD;IACF;IAEQ,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB;QACF;;;AAKA,QAAA,IAAI,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;IACzE;AACD;;AC5QD;;;AAGG;MACU,aAAa,CAAA;AAA1B,IAAA,WAAA,GAAA;QACU,IAAA,CAAA,MAAM,GAAW,EAAE;QACnB,IAAA,CAAA,OAAO,GAAW,wCAAwC;AAC1D,QAAA,IAAA,CAAA,OAAO,GAAW,KAAK,CAAC;IA2QlC;AAzQE;;AAEG;AACH,IAAA,SAAS,CAAC,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;IACtB;AAEA;;AAEG;AACH,IAAA,UAAU,CAAC,OAAe,EAAA;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;IACxB;AAEA;;AAEG;AACH,IAAA,UAAU,CAAC,OAAe,EAAA;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;IACxB;AAEA;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,OAAO,KAAK;QACd;;;AAIA,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;IAClE;AAEA;;AAEG;IACH,MAAM,kBAAkB,CAAC,IAAS,EAAA;AAChC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,MAAM,IAAI,iBAAiB,CACzB,4BAA4B,EAC5B,SAAS,CAAC,eAAe,EACzB,eAAe,CAChB;QACH;AAEA,QAAA,MAAM,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,OAAO,UAAU;AAErC,QAAA,IAAI;AACF,YAAA,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE;AACxC,YAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC;AAEpE,YAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;AAChC,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,OAAO,EAAE;AACP,oBAAA,cAAc,EAAE,kBAAkB;AAClC,oBAAA,eAAe,EAAE,CAAA,OAAA,EAAU,IAAI,CAAC,MAAM,CAAA,CAAE;AACxC,oBAAA,YAAY,EAAE;AACf,iBAAA;AACD,gBAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC1B,MAAM,EAAE,UAAU,CAAC;AACpB,aAAA,CAAC;YAEF,YAAY,CAAC,SAAS,CAAC;AAEvB,YAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AAChB,gBAAA,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YAC1C;AAEA,YAAA,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACpC,YAAA,OAAO,MAAM;QAEf;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,IAAI,KAAK,YAAY,iBAAiB,EAAE;AACtC,gBAAA,MAAM,KAAK;YACb;AAEA,YAAA,IAAI,KAAK,YAAY,KAAK,EAAE;AAC1B,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;AAC/B,oBAAA,MAAM,IAAI,iBAAiB,CACzB,iBAAiB,EACjB,SAAS,CAAC,aAAa,EACvB,SAAS,EACT,IAAI,EACJ,KAAK,CACN;gBACH;AAEA,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AACjE,oBAAA,MAAM,IAAI,iBAAiB,CACzB,2BAA2B,EAC3B,SAAS,CAAC,aAAa,EACvB,SAAS,EACT,IAAI,EACJ,KAAK,CACN;gBACH;YACF;AAEA,YAAA,MAAM,IAAI,iBAAiB,CACzB,wBAAwB,EACxB,SAAS,CAAC,aAAa,EACvB,SAAS,EACT,IAAI,EACJ,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1D;QACH;IACF;AAEA;;AAEG;AACH,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,MAAM,IAAI,iBAAiB,CACzB,4BAA4B,EAC5B,SAAS,CAAC,eAAe,EACzB,eAAe,CAChB;QACH;AAEA,QAAA,MAAM,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,OAAO,SAAS;AAEpC,QAAA,IAAI;AACF,YAAA,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE;AACxC,YAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,UAAU,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;AAE9D,YAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;AAChC,gBAAA,MAAM,EAAE,KAAK;AACb,gBAAA,OAAO,EAAE;AACP,oBAAA,eAAe,EAAE,CAAA,OAAA,EAAU,IAAI,CAAC,MAAM,CAAA,CAAE;AACxC,oBAAA,YAAY,EAAE;AACf,iBAAA;gBACD,MAAM,EAAE,UAAU,CAAC;AACpB,aAAA,CAAC;YAEF,YAAY,CAAC,SAAS,CAAC;YACvB,OAAO,QAAQ,CAAC,EAAE;QAEpB;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,OAAO,KAAK;QACd;IACF;AAEA;;AAEG;IACH,qBAAqB,GAAA;;AAEnB,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,KAAK,WAAW,EAAE;AACvE,YAAA,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC;QAC3E;;AAGA,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACjB,MAAM,IAAI,iBAAiB,CACzB,yBAAyB,EACzB,SAAS,CAAC,mBAAmB,EAC7B,gBAAgB,CACjB;QACH;IACF;AAEA;;AAEG;AACH,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,OAAO,OAAO;AAElC,QAAA,IAAI;AACF,YAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;AAChC,gBAAA,MAAM,EAAE,KAAK;AACb,gBAAA,OAAO,EAAE;AACP,oBAAA,YAAY,EAAE;AACf;AACF,aAAA,CAAC;AAEF,YAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,CAAA,KAAA,EAAQ,QAAQ,CAAC,MAAM,CAAA,CAAE,CAAC;YAC5C;AAEA,YAAA,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE;QAE9B;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,IAAI,iBAAiB,CACzB,+BAA+B,EAC/B,SAAS,CAAC,aAAa,EACvB,SAAS,EACT,IAAI,EACJ,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1D;QACH;IACF;AAEA;;AAEG;IACK,MAAM,mBAAmB,CAAC,QAAkB,EAAA;QAClD,IAAI,YAAY,GAAG,CAAA,KAAA,EAAQ,QAAQ,CAAC,MAAM,CAAA,EAAA,EAAK,QAAQ,CAAC,UAAU,CAAA,CAAE;AACpE,QAAA,IAAI,SAAS,GAAG,SAAS,CAAC,aAAa;AAEvC,QAAA,IAAI;AACF,YAAA,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACvC,YAAA,IAAI,SAAS,CAAC,OAAO,EAAE;AACrB,gBAAA,YAAY,GAAG,SAAS,CAAC,OAAO;YAClC;AACA,YAAA,IAAI,SAAS,CAAC,IAAI,EAAE;AAClB,gBAAA,YAAY,IAAI,CAAA,EAAA,EAAK,SAAS,CAAC,IAAI,GAAG;YACxC;QACF;AAAE,QAAA,MAAM;;QAER;;AAGA,QAAA,QAAQ,QAAQ,CAAC,MAAM;AACrB,YAAA,KAAK,GAAG;AACR,YAAA,KAAK,GAAG;AACN,gBAAA,SAAS,GAAG,SAAS,CAAC,eAAe;gBACrC;AACF,YAAA,KAAK,GAAG;gBACN,YAAY,GAAG,qBAAqB;AACpC,gBAAA,SAAS,GAAG,SAAS,CAAC,aAAa;gBACnC;AACF,YAAA,KAAK,GAAG;AACR,YAAA,KAAK,GAAG;AACR,YAAA,KAAK,GAAG;AACR,YAAA,KAAK,GAAG;gBACN,YAAY,GAAG,cAAc;AAC7B,gBAAA,SAAS,GAAG,SAAS,CAAC,aAAa;gBACnC;AACF,YAAA;AACE,gBAAA,SAAS,GAAG,SAAS,CAAC,aAAa;;AAGvC,QAAA,MAAM,IAAI,iBAAiB,CACzB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,QAAQ,CAAC,MAAM,IAAI,GAAG;SACvB;IACH;AAEA;;AAEG;AACH,IAAA,OAAO,mBAAmB,GAAA;QACxB,MAAM,OAAO,GAAa,EAAE;AAE5B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AACjB,YAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;QAC3B;AAEA,QAAA,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;AAC3B,YAAA,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;QACjC;AAEA,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;AACf,YAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;QACzB;QAEA,OAAO;AACL,YAAA,SAAS,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YAC/B;SACD;IACH;AACD;;AClQD;;;AAGG;iCACU,kBAAkB,CAAA;AAiB7B,IAAA,WAAA,GAAA;QAZQ,IAAA,CAAA,YAAY,GAAQ,IAAI;QAGxB,IAAA,CAAA,MAAM,GAA8B,IAAI;QACxC,IAAA,CAAA,WAAW,GAA4B,IAAI;QAC3C,IAAA,CAAA,SAAS,GAAG,KAAK;QACjB,IAAA,CAAA,YAAY,GAAkB,EAAE;QAChC,IAAA,CAAA,cAAc,GAAsC,EAAE;QACtD,IAAA,CAAA,aAAa,GAAyB,IAAI;QAC1C,IAAA,CAAA,UAAU,GAAc,EAAE;QAC1B,IAAA,CAAA,eAAe,GAAc,EAAE;AAGrC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB,EAAE;AACpD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAE;IAC1C;AAEA;;AAEG;AACH,IAAA,aAAa,UAAU,CAAC,OAAyB,EAAA;AAC/C,QAAA,IAAI,kBAAkB,CAAC,WAAW,EAAE;YAClC;QACF;AAEA,QAAA,IAAI;;AAEF,YAAA,MAAM,OAAO,GAAG,kBAAkB,CAAC,iBAAiB,EAAE;AACtD,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACpB,MAAM,IAAI,iBAAiB,CACzB,CAAA,gCAAA,EAAmC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,EAC/D,SAAS,CAAC,mBAAmB,EAC7B,gBAAgB,CACjB;YACH;;AAGA,YAAA,MAAM,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,6BAA6B;;AAGnE,YAAA,MAAM,iBAAiB,GAAG,MAAM,OAAO,QAAQ,CAAC;YAChD,kBAAkB,CAAC,UAAU,GAAG,MAAM,iBAAiB,CAAC,OAAO,EAAE;AAEjE,YAAA,kBAAkB,CAAC,WAAW,GAAG,IAAI;QAEvC;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,IAAI,iBAAiB,CACzB,kCAAkC,EAClC,SAAS,CAAC,gBAAgB,EAC1B,gBAAgB,EAChB,KAAK,EACL,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1D;QACH;IACF;AAEA;;AAEG;AACH,IAAA,OAAO,WAAW,GAAA;AAChB,QAAA,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;YACnC,MAAM,IAAI,iBAAiB,CACzB,iEAAiE,EACjE,SAAS,CAAC,eAAe,EACzB,gBAAgB,CACjB;QACH;AAEA,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE;AAChC,YAAA,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE;QACxD;QAEA,OAAO,kBAAkB,CAAC,QAAQ;IACpC;AAEA;;AAEG;AACH,IAAA,OAAO,UAAU,GAAA;QACf,OAAO,OAAO,CAAC;IACjB;AAEA;;AAEG;AACH,IAAA,OAAO,WAAW,GAAA;AAChB,QAAA,OAAO,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,OAAO;IACvD;AAEA;;AAEG;AACH,IAAA,OAAO,iBAAiB,GAAA;QACtB,MAAM,OAAO,GAAa,EAAE;;AAG5B,QAAA,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;AACvB,YAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;QAC7B;;AAGA,QAAA,MAAM,YAAY,GAAG,mBAAmB,CAAC,mBAAmB,EAAE;AAC9D,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE;YAC3B,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC;QACvC;;AAGA,QAAA,MAAM,cAAc,GAAG,aAAa,CAAC,mBAAmB,EAAE;AAC1D,QAAA,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,OAAO,CAAC;QACzC;;AAGA,QAAA,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,GAAG,EAAE;AAC5B,YAAA,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;QACjC;AAEA,QAAA,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;AACjC,YAAA,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC;QACvC;QAEA,OAAO;AACL,YAAA,WAAW,EAAE,MAAM,CAAC,WAAW,KAAK,SAAS;AAC7C,YAAA,YAAY,EAAE,CAAC,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY;AACpD,YAAA,MAAM,EAAE,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,UAAU;AAChD,YAAA,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK;AACrB,YAAA,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,qBAAqB;AACrC,YAAA,iBAAiB,EAAE,CAAC,CAAC,MAAM,CAAC,iBAAiB;AAC7C,YAAA,OAAO,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YAC7B;SACD;IACH;AAEA;;AAEG;AACH,IAAA,OAAO,MAAM,GAAA;AACX,QAAA,IAAI,kBAAkB,CAAC,QAAQ,EAAE;AAC/B,YAAA,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE;AACrC,YAAA,kBAAkB,CAAC,QAAQ,GAAG,IAAI;QACpC;AACA,QAAA,kBAAkB,CAAC,UAAU,GAAG,IAAI;AACpC,QAAA,kBAAkB,CAAC,WAAW,GAAG,KAAK;IACxC;AAEA;;AAEG;AACH,IAAA,SAAS,CAAC,MAA0B,EAAA;AAClC,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE;;AAG3B,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;QAC7C;;AAGA,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,sBAAsB,EAAE;QAC/B;;QAGA,IAAI,CAAC,qBAAqB,EAAE;IAC9B;AAEA;;AAEG;AACH,IAAA,SAAS,CAAC,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE;QAC/C;AACA,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC;IACtC;AAEA,IAAA,sBAAsB,CAAC,QAAgB,EAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE;QAC/C;;QAEA,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAClE;AAEA,IAAA,mBAAmB,CAAC,IAAsB,EAAA;AACxC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE;QACxB;aAAO;AACL,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI;QACzB;IACF;AAEA,IAAA,iBAAiB,CAAC,QAA0B,EAAA;AAC1C,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE;QAC/C;AACA,QAAA,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,QAAQ;IACvC;AAEA;;AAEG;IACH,MAAM,eAAe,CAAC,YAA8B,EAAA;QAClD,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,YAAY,CAAC;IAC9D;IAEA,MAAM,cAAc,CAAC,MAAmB,EAAA;QACtC,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,MAAM,CAAC;IACvD;AAEA;;AAEG;AACH,IAAA,MAAM,uBAAuB,GAAA;QAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,gBAAgB,IAAI,EAAE;AACvD,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE;AAC/B,YAAA,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,KAAK,OAAO,GAAG,MAAM,GAAG,aAAa;QAC1F;QACA,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,WAAW,CAAC;IAC5E;AAEA;;AAEG;IACH,MAAM,KAAK,CAAC,OAAqB,EAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,iBAAiB,CACzB,iBAAiB,EACjB,SAAS,CAAC,eAAe,EACzB,SAAS,CACV;QACH;AAEA,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,iBAAiB,CACzB,+BAA+B,EAC/B,SAAS,CAAC,eAAe,EACzB,SAAS,CACV;QACH;AAEA,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGrB,YAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;;YAGhC,IAAI,OAAO,GAAG,KAAK;YACnB,IAAI,OAAO,CAAC,IAAI,KAAK,gBAAgB,CAAC,IAAI,EAAE;AAC1C,gBAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE;gBACpE,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC;YACrD;iBAAO;AACL,gBAAA,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE;YACnD;YAEA,IAAI,CAAC,OAAO,EAAE;AACZ,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,gBAAA,MAAM,IAAI,iBAAiB,CACzB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,6BAA6B,EACjE,SAAS,CAAC,iBAAiB,EAC3B,SAAS,CACV;YACH;;YAGA,IAAI,CAAC,mBAAmB,EAAE;;YAG1B,IAAI,OAAO,CAAC,IAAI,KAAK,gBAAgB,CAAC,IAAI,EAAE;gBAC1C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,oBAAA,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ;AACzC,oBAAA,OAAO,CAAC,QAAQ,GAAG,CAAC,MAAqB,KAAI;AAC3C,wBAAA,IAAI,gBAAgB;4BAAE,gBAAgB,CAAC,MAAM,CAAC;wBAC9C,OAAO,CAAC,MAAM,CAAC;AACjB,oBAAA,CAAC;AAED,oBAAA,MAAM,eAAe,GAAG,OAAO,CAAC,OAAO;AACvC,oBAAA,OAAO,CAAC,OAAO,GAAG,CAAC,KAAwB,KAAI;AAC7C,wBAAA,IAAI,eAAe;4BAAE,eAAe,CAAC,KAAK,CAAC;wBAC3C,MAAM,CAAC,KAAK,CAAC;AACf,oBAAA,CAAC;AACH,gBAAA,CAAC,CAAC;YACJ;QAEF;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,MAAM,KAAK;QACb;IACF;AAEA;;AAEG;AACH,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;QACF;AAEA,QAAA,IAAI;AACF,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;YAC1B;AACA,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;QAEzB;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,IAAI,iBAAiB,CACzB,4BAA4B,EAC5B,SAAS,CAAC,iBAAiB,EAC3B,SAAS,EACT,KAAK,EACL,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1D;QACH;IACF;AAEA;;AAEG;IACH,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,eAAe,GAAA;;AAEb,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,gBAAgB,CAAC,UAAU,GAAG,EAAE,GAAG,CAAC;IAClE;IAEA,YAAY,GAAA;QACV,OAAO;AACL,YAAA,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,gBAAgB,CAAC,IAAI;AAC/C,YAAA,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE;YAChC,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,GAAG,UAAU,CAAC,sBAAsB;YAC5H,QAAQ,EAAE,KAAK;SAChB;IACH;AAEA;;AAEG;AACH,IAAA,IAAI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,aAAa;IAC3B;AAEA,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,UAAU;IACxB;AAEA,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,eAAe;IAC7B;AAEA;;AAEG;IACH,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,YAAY,KAAK,IAAI;IACnC;IAEA,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,UAAU,GAAA;AACR,QAAA,OAAO,kBAAkB,CAAC,UAAU,EAAE;IACxC;AAEA;;AAEG;IACH,YAAY,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,GAAG,EAAE;IAClE;AAEA,IAAA,gBAAgB,CAAC,QAAuC,EAAA;AACtD,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;IACpC;AAEA,IAAA,mBAAmB,CAAC,QAAuC,EAAA;QACzD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC;AACnD,QAAA,IAAI,KAAK,GAAG,EAAE,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QACtC;IACF;IAEA,eAAe,GAAA;AACb,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;IAC/B;IAEA,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE;IACxB;AAEA;;AAEG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,IAAI,CAAC,IAAI,EAAE;AACjB,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE;AAClC,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QAC1B;AACA,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;AAClB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACpB,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE;AACtB,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE;IAC1B;AAEA;;AAEG;IACK,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE;YAClC,MAAM,IAAI,iBAAiB,CACzB,wBAAwB,EACxB,SAAS,CAAC,eAAe,EACzB,gBAAgB,CACjB;QACH;QAEA,IAAI,CAAC,YAAY,GAAG,IAAI,kBAAkB,CAAC,UAAU,CAAC,gBAAgB,EAAE;IAC1E;IAEQ,qBAAqB,GAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACtC;QACF;AAEA,QAAA,MAAM,UAAU,GAAe;AAC7B,YAAA,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;AACtB,YAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE;AACjC,YAAA,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;AACzC,YAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;AAC7C,YAAA,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;AACnC,YAAA,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;AAC5C,YAAA,4BAA4B,EAAE,IAAI,CAAC,MAAM,CAAC,yBAAyB;AACnE,YAAA,6BAA6B,EAAE,IAAI,CAAC,MAAM,CAAC,yBAAyB;AACpE,YAAA,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;AAClD,YAAA,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC;SAC9B;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,MAAM,IAAI,iBAAiB,CACzB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,2BAA2B,EAC/D,SAAS,CAAC,cAAc,EACxB,eAAe,CAChB;QACH;IACF;AAEQ,IAAA,kBAAkB,CAAC,OAAqB,EAAA;QAC9C,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE;;QAGxB,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,WAAmB,EAAE,SAAiB,KAAI;AAChF,YAAA,IAAI;gBACF,MAAM,OAAO,GAAkB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AACtD,gBAAA,IAAI,CAAC,aAAa,GAAG,OAAO;AAC5B,gBAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,oBAAA,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC3B;YACF;YAAE,OAAO,KAAK,EAAE;AACd,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,iBAAiB,CACpC,yBAAyB,EACzB,SAAS,CAAC,iBAAiB,EAC3B,SAAS,EACT,KAAK,EACL,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1D,CAAC;YACJ;AACF,QAAA,CAAC,CAAC;;QAGF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,UAAkB,KAAI;YAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;AACjD,YAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,gBAAA,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC1B;AACF,QAAA,CAAC,CAAC;;QAGF,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,YAAoB,KAAI;AAC5D,YAAA,MAAM,KAAK,GAAG,IAAI,iBAAiB,CACjC,YAAY,EACZ,SAAS,CAAC,iBAAiB,EAC3B,SAAS,CACV;AACD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;AACvB,YAAA,IAAI,OAAO,CAAC,OAAO,EAAE;AACnB,gBAAA,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;YACxB;AACF,QAAA,CAAC,CAAC;IACJ;IAEQ,mBAAmB,GAAA;AACzB,QAAA,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE;QACvC,IAAI,CAAC,gBAAgB,EAAE;IACzB;IAEQ,gBAAgB,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACzC;QACF;AAEA,QAAA,IAAI;YACF,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE;YACzD,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAC5C,KAAK,CAAC,SAAS,EACf,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,SAAS,CAChB;YAED,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;gBAC9C,IAAI,KAAK,EAAE;AACT,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,iBAAiB,CACpC,KAAK,EACL,SAAS,CAAC,iBAAiB,EAC3B,SAAS,CACV,CAAC;gBACJ;YACF;QAEF;QAAE,OAAO,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,iBAAiB,CACpC,yBAAyB,EACzB,SAAS,CAAC,iBAAiB,EAC3B,SAAS,EACT,IAAI,EACJ,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1D,CAAC;QACJ;;AAGA,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,qBAAqB,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtD;IACF;AAEQ,IAAA,iBAAiB,CAAC,IAAY,EAAA;AACpC,QAAA,MAAM,SAAS,GAA+B;YAC5C,CAAC,EAAE,UAAU,CAAC,EAAE;YAChB,CAAC,EAAE,UAAU,CAAC,cAAc;YAC5B,CAAC,EAAE,UAAU,CAAC,wBAAwB;YACtC,CAAC,EAAE,UAAU,CAAC,iBAAiB;YAC/B,CAAC,EAAE,UAAU,CAAC,yBAAyB;YACvC,CAAC,EAAE,UAAU,CAAC,cAAc;YAC5B,CAAC,EAAE,UAAU,CAAC,gBAAgB;YAC9B,CAAC,EAAE,UAAU,CAAC,mCAAmC;YACjD,CAAC,EAAE,UAAU,CAAC;SACf;QACD,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,sBAAsB;IAC7D;AAEQ,IAAA,WAAW,CAAC,KAAwB,EAAA;AAC1C,QAAA,MAAM,MAAM,GAAgB;YAC1B,KAAK;AACL,YAAA,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;AACrB,YAAA,OAAO,EAAE;gBACP,IAAI,EAAE,IAAI,CAAC,WAAW;gBACtB,SAAS,EAAE,IAAI,CAAC,SAAS;AACzB,gBAAA,aAAa,EAAE,IAAI,CAAC,aAAa;AAClC,aAAA;AACD,YAAA,QAAQ,EAAE;gBACR,UAAU,EAAE,KAAK,CAAC,WAAW;gBAC7B,cAAc,EAAE,KAAK,CAAC,WAAW,GAAG,iBAAiB,GAAG,aAAa;AACrE,gBAAA,QAAQ,EAAE;AACX;SACF;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;;QAG9B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,EAAE;AAClC,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;QAC3B;;AAGA,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,IAAG;AACrC,YAAA,IAAI;gBACF,QAAQ,CAAC,MAAM,CAAC;YAClB;YAAE,OAAO,aAAa,EAAE;AACtB,gBAAA,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,aAAa,CAAC;YAC1D;AACF,QAAA,CAAC,CAAC;IACJ;;AAxlBeA,oBAAA,CAAA,QAAQ,GAA8B,IAA9B;AACRA,oBAAA,CAAA,UAAU,GAAQ,IAAR;AACVA,oBAAA,CAAA,WAAW,GAAG,KAAH;;ACzB5B;;;;;;;;AAQG;AAEH;AAsCA;AACA,2BAAe,kBAAkB;;;;"}