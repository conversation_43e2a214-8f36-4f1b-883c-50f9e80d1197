/**
 * Network client for SmartSpectra API communication
 * Handles REST API calls with API key authentication
 */
export declare class NetworkClient {
    private apiKey;
    private baseUrl;
    private timeout;
    /**
     * Set API key for authentication
     */
    setApiKey(apiKey: string): void;
    /**
     * Set base URL for API calls
     */
    setBaseUrl(baseUrl: string): void;
    /**
     * Set request timeout
     */
    setTimeout(timeout: number): void;
    /**
     * Validate API key format
     */
    validateApiKey(): boolean;
    /**
     * Send metrics request to the API
     */
    sendMetricsRequest(data: any): Promise<any>;
    /**
     * Test API connectivity and authentication
     */
    testConnection(): Promise<boolean>;
    /**
     * Handle CORS and security requirements
     */
    handleCorsAndSecurity(): void;
    /**
     * Get API status and version information
     */
    getApiInfo(): Promise<{
        version: string;
        status: string;
    }>;
    /**
     * Handle error responses from the API
     */
    private handleErrorResponse;
    /**
     * Check if the current environment supports the required network features
     */
    static checkNetworkSupport(): {
        supported: boolean;
        missing: string[];
    };
}
//# sourceMappingURL=network-client.d.ts.map