/**
 * TypeScript definitions for SmartSpectra WASM-JS SDK
 * Aligned with protobuf definitions and native SDK interfaces
 */
export declare enum SmartSpectraMode {
    SPOT = "spot",
    CONTINUOUS = "continuous"
}
export type QualityLevel = 'good' | 'fair' | 'poor';
export type ErrorCategory = 'initialization' | 'configuration' | 'runtime' | 'resource';
export declare enum StatusCode {
    OK = "OK",
    NO_FACES_FOUND = "NO_FACES_FOUND",
    MORE_THAN_ONE_FACE_FOUND = "MORE_THAN_ONE_FACE_FOUND",
    FACE_NOT_CENTERED = "FACE_NOT_CENTERED",
    FACE_TOO_BIG_OR_TOO_SMALL = "FACE_TOO_BIG_OR_TOO_SMALL",
    IMAGE_TOO_DARK = "IMAGE_TOO_DARK",
    IMAGE_TOO_BRIGHT = "IMAGE_TOO_BRIGHT",
    CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING = "CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING",
    PROCESSING_NOT_STARTED = "PROCESSING_NOT_STARTED"
}
export interface SmartSpectraConfig {
    apiKey?: string;
    mode: SmartSpectraMode;
    spotDuration?: number;
    bufferDuration?: number;
    wasmPath?: string;
    debug?: boolean;
    cameraPosition?: 'front' | 'back';
    videoConstraints?: MediaTrackConstraints;
    scaleInput?: boolean;
    enablePhasicBP?: boolean;
    enableDenseFacemeshPoints?: boolean;
    useFullRangeFaceDetection?: boolean;
    enableEdgeMetrics?: boolean;
    verbosityLevel?: number;
}
export interface MetricsBuffer {
    pulse?: Pulse;
    breathing?: Breathing;
    bloodPressure?: BloodPressure;
    face?: Face;
    metadata?: Metadata;
}
export interface Pulse {
    rate: MeasurementWithConfidence[];
    trace: Measurement[];
    pulseRespirationQuotient: Measurement[];
    strict?: Strict;
}
export interface Breathing {
    rate: MeasurementWithConfidence[];
    upperTrace: Measurement[];
    lowerTrace: Measurement[];
    amplitude: Measurement[];
    apnea: DetectionStatus[];
    respiratoryLineLength: Measurement[];
    baseline: Measurement[];
    inhaleExhaleRatio: Measurement[];
    strict?: Strict;
}
export interface BloodPressure {
    phasic: MeasurementWithConfidence[];
}
export interface Face {
    blinking: DetectionStatus[];
    talking: DetectionStatus[];
    landmarks: Landmarks[];
}
export interface Landmarks {
    time: number;
    value: Point2D[];
    stable: boolean;
}
export interface Point2D {
    x: number;
    y: number;
}
export interface Metadata {
    id: string;
    uploadTimestamp: string;
    apiVersion: string;
    sentAtS: number;
    frameTimestamp: number;
    frameCount: number;
}
export interface Measurement {
    time: number;
    value: number;
    stable: boolean;
}
export interface MeasurementWithConfidence extends Measurement {
    confidence: number;
}
export interface DetectionStatus {
    time: number;
    detected: boolean;
    stable: boolean;
}
export interface Strict {
    value: number;
}
export interface ModeState {
    mode: SmartSpectraMode;
    progress: number;
    frameCount: number;
    duration: number;
    quality: QualityLevel;
    status: StatusCode;
    isStable: boolean;
}
export interface SmartSpectraCallbacks {
    onResult?: (result: MetricsBuffer) => void;
    onStatus?: (status: StatusCode) => void;
    onError?: (error: SmartSpectraError) => void;
    onModeComplete?: (mode: SmartSpectraMode, results: MetricsBuffer[]) => void;
    onMeshPointsUpdate?: (points: Point2D[]) => void;
    onFrameSentThrough?: (frameSent: boolean, timestamp: number) => void;
}
export interface StartOptions {
    mode: SmartSpectraMode;
    duration?: number;
    onResult?: (result: MetricsBuffer) => void;
    onStatus?: (status: StatusCode) => void;
    onError?: (error: SmartSpectraError) => void;
}
export declare enum ErrorCode {
    WASM_LOAD_FAILED = "WASM_LOAD_FAILED",
    CAMERA_ACCESS_DENIED = "CAMERA_ACCESS_DENIED",
    INVALID_API_KEY = "INVALID_API_KEY",
    NETWORK_ERROR = "NETWORK_ERROR",
    PROCESSING_FAILED = "PROCESSING_FAILED",
    MEMORY_ERROR = "MEMORY_ERROR",
    INVALID_CONFIG = "INVALID_CONFIG",
    NOT_INITIALIZED = "NOT_INITIALIZED",
    ALREADY_RUNNING = "ALREADY_RUNNING",
    NOT_RUNNING = "NOT_RUNNING",
    UNSUPPORTED_BROWSER = "UNSUPPORTED_BROWSER"
}
export declare class SmartSpectraError extends Error {
    code: ErrorCode;
    category: ErrorCategory;
    recoverable: boolean;
    cause?: Error | undefined;
    constructor(message: string, code: ErrorCode, category: ErrorCategory, recoverable?: boolean, cause?: Error | undefined);
}
export interface ErrorReport {
    error: SmartSpectraError;
    timestamp: number;
    context: Record<string, any>;
    recovery: {
        canRecover: boolean;
        recoveryAction?: string;
        attempts: number;
    };
}
export interface BrowserSupport {
    webAssembly: boolean;
    getUserMedia: boolean;
    canvas: boolean;
    fetch: boolean;
    webGL: boolean;
    sharedArrayBuffer: boolean;
    overall: boolean;
    missing: string[];
}
export interface WASMLoadOptions {
    wasmPath?: string;
    debug?: boolean;
    memoryInitialSize?: number;
    memoryMaximumSize?: number;
}
export interface VideoFrame {
    pixelData: Uint8Array;
    width: number;
    height: number;
    timestamp: number;
    format: 'RGB' | 'RGBA' | 'BGR';
}
export interface WASMConfig {
    mode: 'spot' | 'continuous';
    spot_duration_s?: number;
    buffer_duration_s?: number;
    api_key: string;
    scale_input?: boolean;
    enable_phasic_bp?: boolean;
    enable_dense_facemesh_points?: boolean;
    use_full_range_face_detection?: boolean;
    enable_edge_metrics?: boolean;
    verbosity_level?: number;
}
//# sourceMappingURL=types.d.ts.map