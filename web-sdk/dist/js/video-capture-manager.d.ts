import { VideoFrame } from './types';
/**
 * Manages video capture and frame extraction for the SmartSpectra SDK
 * Handles getUserMedia, canvas operations, and RGB pixel data extraction
 */
export declare class VideoCaptureManager {
    private canvas;
    private context;
    private videoElement;
    private stream;
    private animationFrameId;
    private isCapturing;
    constructor();
    /**
     * Request camera permission and get media stream
     */
    requestCameraPermission(constraints?: MediaTrackConstraints): Promise<MediaStream>;
    /**
     * Initialize with a video element
     */
    setVideoElement(videoElement: HTMLVideoElement): Promise<void>;
    /**
     * Set video stream
     */
    setVideoStream(stream: MediaStream): Promise<void>;
    /**
     * Start capturing frames
     */
    startCapture(): void;
    /**
     * Stop capturing frames
     */
    stopCapture(): void;
    /**
     * Extract RGB pixel data from current video frame
     */
    extractPixelData(): VideoFrame;
    /**
     * Get frame dimensions
     */
    getFrameDimensions(): {
        width: number;
        height: number;
    };
    /**
     * Get current timestamp
     */
    getCurrentTimestamp(): number;
    /**
     * Cleanup resources
     */
    cleanup(): void;
    /**
     * Check if browser supports required features
     */
    static checkBrowserSupport(): {
        supported: boolean;
        missing: string[];
    };
    private updateCanvasSize;
    private captureLoop;
}
//# sourceMappingURL=video-capture-manager.d.ts.map