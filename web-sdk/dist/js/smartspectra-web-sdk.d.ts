import { SmartSpectraConfig, SmartSpectraMode, MetricsBuffer, StartOptions, ModeState, Point2D, ErrorReport, BrowserSupport, WASMLoadOptions } from './types';
/**
 * Main SmartSpectra Web SDK class
 * Provides a high-level JavaScript API for heart rate and respiration measurement
 */
export declare class SmartSpectraWebSDK {
    private static instance;
    private static wasmModule;
    private static initialized;
    private wasmInstance;
    private videoCaptureManager;
    private networkClient;
    private config;
    private currentMode;
    private isRunning;
    private errorHistory;
    private errorListeners;
    private metricsBuffer;
    private meshPoints;
    private denseMeshPoints;
    private constructor();
    /**
     * Static initialization - loads WASM module
     */
    static initialize(options?: WASMLoadOptions): Promise<void>;
    /**
     * Get singleton instance
     */
    static getInstance(): SmartSpectraWebSDK;
    /**
     * Get SDK version
     */
    static getVersion(): string;
    /**
     * Check if browser is supported
     */
    static isSupported(): boolean;
    /**
     * Get detailed browser support information
     */
    static getBrowserSupport(): BrowserSupport;
    /**
     * Unload the SDK and cleanup resources
     */
    static unload(): void;
    /**
     * Configure the SDK
     */
    configure(config: SmartSpectraConfig): void;
    /**
     * Individual setter methods (like Swift/Android)
     */
    setApiKey(apiKey: string): void;
    setMeasurementDuration(duration: number): void;
    setSmartSpectraMode(mode: SmartSpectraMode): void;
    setCameraPosition(position: 'front' | 'back'): void;
    /**
     * Video handling methods
     */
    setVideoElement(videoElement: HTMLVideoElement): Promise<void>;
    setVideoStream(stream: MediaStream): Promise<void>;
    /**
     * Request camera permission
     */
    requestCameraPermission(): Promise<MediaStream>;
    /**
     * Start measurement
     */
    start(options: StartOptions): Promise<MetricsBuffer | void>;
    /**
     * Stop measurement
     */
    stop(): Promise<void>;
    /**
     * State management methods
     */
    getCurrentMode(): SmartSpectraMode | null;
    getModeProgress(): number;
    getModeState(): ModeState;
    /**
     * Property-based access (like Swift/Android)
     */
    get metricsBuffer(): MetricsBuffer | null;
    get meshPoints(): Point2D[];
    get denseMeshPoints(): Point2D[];
    /**
     * Utility methods
     */
    isInitialized(): boolean;
    isRunning(): boolean;
    getVersion(): string;
    /**
     * Error handling methods
     */
    getLastError(): string;
    addErrorListener(listener: (report: ErrorReport) => void): void;
    removeErrorListener(listener: (report: ErrorReport) => void): void;
    getErrorHistory(): ErrorReport[];
    clearErrorHistory(): void;
    /**
     * Cleanup and disposal
     */
    dispose(): Promise<void>;
    /**
     * Private methods
     */
    private initializeWASMInstance;
    private configureWASMInstance;
    private setupWASMCallbacks;
    private startProcessingLoop;
    private processFrameLoop;
    private getStatusFromCode;
    private handleError;
}
//# sourceMappingURL=smartspectra-web-sdk.d.ts.map