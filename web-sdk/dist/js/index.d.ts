/**
 * SmartSpectra Web SDK
 *
 * A WebAssembly-based JavaScript SDK for integrating SmartSpectra's
 * heart rate and respiration rate measurement capabilities into web applications.
 *
 * @version 1.0.0
 * <AUTHOR> Technologies
 */
export { SmartSpectraWebSDK } from './smartspectra-web-sdk';
export { SmartSpectraMode, StatusCode, ErrorCode, SmartSpectraError, type SmartSpectraConfig, type MetricsBuffer, type Pulse, type Breathing, type BloodPressure, type Face, type Metadata, type Measurement, type MeasurementWithConfidence, type DetectionStatus, type Strict, type Landmarks, type Point2D, type ModeState, type SmartSpectraCallbacks, type StartOptions, type ErrorReport, type BrowserSupport, type WASMLoadOptions, type VideoFrame, type WASMConfig, type QualityLevel, type ErrorCategory } from './types';
export { VideoCaptureManager } from './video-capture-manager';
export { NetworkClient } from './network-client';
export default SmartSpectraWebSDK;
//# sourceMappingURL=index.d.ts.map