# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/smartspectra_wasm.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/smartspectra_wasm.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/smartspectra_wasm.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/smartspectra_wasm.dir

# All Build rule for target.
CMakeFiles/smartspectra_wasm.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smartspectra_wasm.dir/build.make CMakeFiles/smartspectra_wasm.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smartspectra_wasm.dir/build.make CMakeFiles/smartspectra_wasm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles --progress-num=1,2,3 "Built target smartspectra_wasm"
.PHONY : CMakeFiles/smartspectra_wasm.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/smartspectra_wasm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/smartspectra_wasm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles 0
.PHONY : CMakeFiles/smartspectra_wasm.dir/rule

# Convenience name for target.
smartspectra_wasm: CMakeFiles/smartspectra_wasm.dir/rule
.PHONY : smartspectra_wasm

# codegen rule for target.
CMakeFiles/smartspectra_wasm.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smartspectra_wasm.dir/build.make CMakeFiles/smartspectra_wasm.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles --progress-num=1,2,3 "Finished codegen for target smartspectra_wasm"
.PHONY : CMakeFiles/smartspectra_wasm.dir/codegen

# clean rule for target.
CMakeFiles/smartspectra_wasm.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/smartspectra_wasm.dir/build.make CMakeFiles/smartspectra_wasm.dir/clean
.PHONY : CMakeFiles/smartspectra_wasm.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

