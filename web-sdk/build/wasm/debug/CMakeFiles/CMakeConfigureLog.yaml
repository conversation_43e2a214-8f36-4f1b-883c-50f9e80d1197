
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/uname"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/uname"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/uname"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/uname"
      - "/Users/<USER>/Documents/node_modules/.bin/uname"
      - "/Users/<USER>/node_modules/.bin/uname"
      - "/Users/<USER>/.bin/uname"
      - "/node_modules/.bin/uname"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/uname"
      - "/usr/local/bin/uname"
      - "/System/Cryptexes/App/usr/bin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:207 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Darwin - 24.5.0 - arm64
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeUnixFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmake"
      - "make"
      - "smake"
    candidate_directories:
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/gmake"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/gmake"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/gmake"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/gmake"
      - "/Users/<USER>/Documents/node_modules/.bin/gmake"
      - "/Users/<USER>/node_modules/.bin/gmake"
      - "/Users/<USER>/.bin/gmake"
      - "/node_modules/.bin/gmake"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/gmake"
      - "/usr/local/bin/gmake"
      - "/System/Cryptexes/App/usr/bin/gmake"
      - "/usr/bin/gmake"
      - "/bin/gmake"
      - "/usr/sbin/gmake"
      - "/sbin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/gmake"
      - "/Library/Apple/usr/bin/gmake"
      - "/Users/<USER>/.codeium/windsurf/bin/gmake"
      - "/Users/<USER>/.deno/bin/gmake"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/gmake"
      - "/usr/local/sbin/gmake"
      - "/opt/homebrew/bin/gmake"
      - "/opt/homebrew/sbin/gmake"
      - "/Users/<USER>/.local/bin/gmake"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/gmake"
      - "/bin/gmake"
      - "/sbin/gmake"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/make"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/make"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/make"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/make"
      - "/Users/<USER>/Documents/node_modules/.bin/make"
      - "/Users/<USER>/node_modules/.bin/make"
      - "/Users/<USER>/.bin/make"
      - "/node_modules/.bin/make"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/make"
      - "/usr/local/bin/make"
      - "/System/Cryptexes/App/usr/bin/make"
    found: "/usr/bin/make"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-strip"
      - "strip"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/llvm-strip"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/llvm-strip"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/llvm-strip"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/llvm-strip"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/llvm-strip"
      - "/Users/<USER>/Documents/node_modules/.bin/llvm-strip"
      - "/Users/<USER>/node_modules/.bin/llvm-strip"
      - "/Users/<USER>/.bin/llvm-strip"
      - "/node_modules/.bin/llvm-strip"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/llvm-strip"
      - "/usr/local/bin/llvm-strip"
      - "/System/Cryptexes/App/usr/bin/llvm-strip"
      - "/usr/bin/llvm-strip"
      - "/bin/llvm-strip"
      - "/usr/sbin/llvm-strip"
      - "/sbin/llvm-strip"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/llvm-strip"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/llvm-strip"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/llvm-strip"
      - "/Library/Apple/usr/bin/llvm-strip"
      - "/Users/<USER>/.codeium/windsurf/bin/llvm-strip"
      - "/Users/<USER>/.deno/bin/llvm-strip"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/llvm-strip"
      - "/usr/local/sbin/llvm-strip"
      - "/opt/homebrew/bin/llvm-strip"
      - "/opt/homebrew/sbin/llvm-strip"
      - "/Users/<USER>/.local/bin/llvm-strip"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-strip"
      - "/bin/llvm-strip"
      - "/sbin/llvm-strip"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/strip"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/strip"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/strip"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/strip"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/strip"
      - "/Users/<USER>/Documents/node_modules/.bin/strip"
      - "/Users/<USER>/node_modules/.bin/strip"
      - "/Users/<USER>/.bin/strip"
      - "/node_modules/.bin/strip"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/strip"
      - "/usr/local/bin/strip"
      - "/System/Cryptexes/App/usr/bin/strip"
    found: "/usr/bin/strip"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld.lld"
      - "ld"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/ld.lld"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/ld.lld"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/ld.lld"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/ld.lld"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/ld.lld"
      - "/Users/<USER>/Documents/node_modules/.bin/ld.lld"
      - "/Users/<USER>/node_modules/.bin/ld.lld"
      - "/Users/<USER>/.bin/ld.lld"
      - "/node_modules/.bin/ld.lld"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/ld.lld"
      - "/usr/local/bin/ld.lld"
      - "/System/Cryptexes/App/usr/bin/ld.lld"
      - "/usr/bin/ld.lld"
      - "/bin/ld.lld"
      - "/usr/sbin/ld.lld"
      - "/sbin/ld.lld"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/ld.lld"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/ld.lld"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/ld.lld"
      - "/Library/Apple/usr/bin/ld.lld"
      - "/Users/<USER>/.codeium/windsurf/bin/ld.lld"
      - "/Users/<USER>/.deno/bin/ld.lld"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/ld.lld"
      - "/usr/local/sbin/ld.lld"
      - "/opt/homebrew/bin/ld.lld"
      - "/opt/homebrew/sbin/ld.lld"
      - "/Users/<USER>/.local/bin/ld.lld"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/ld.lld"
      - "/bin/ld.lld"
      - "/sbin/ld.lld"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/ld"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/ld"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/ld"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/ld"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/ld"
      - "/Users/<USER>/Documents/node_modules/.bin/ld"
      - "/Users/<USER>/node_modules/.bin/ld"
      - "/Users/<USER>/.bin/ld"
      - "/node_modules/.bin/ld"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/ld"
      - "/usr/local/bin/ld"
      - "/System/Cryptexes/App/usr/bin/ld"
    found: "/usr/bin/ld"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-objdump"
      - "objdump"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/llvm-objdump"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/llvm-objdump"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/llvm-objdump"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/llvm-objdump"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/llvm-objdump"
      - "/Users/<USER>/Documents/node_modules/.bin/llvm-objdump"
      - "/Users/<USER>/node_modules/.bin/llvm-objdump"
      - "/Users/<USER>/.bin/llvm-objdump"
      - "/node_modules/.bin/llvm-objdump"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/llvm-objdump"
      - "/usr/local/bin/llvm-objdump"
      - "/System/Cryptexes/App/usr/bin/llvm-objdump"
      - "/usr/bin/llvm-objdump"
      - "/bin/llvm-objdump"
      - "/usr/sbin/llvm-objdump"
      - "/sbin/llvm-objdump"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/llvm-objdump"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/llvm-objdump"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/llvm-objdump"
      - "/Library/Apple/usr/bin/llvm-objdump"
      - "/Users/<USER>/.codeium/windsurf/bin/llvm-objdump"
      - "/Users/<USER>/.deno/bin/llvm-objdump"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/llvm-objdump"
      - "/usr/local/sbin/llvm-objdump"
      - "/opt/homebrew/bin/llvm-objdump"
      - "/opt/homebrew/sbin/llvm-objdump"
      - "/Users/<USER>/.local/bin/llvm-objdump"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-objdump"
      - "/bin/llvm-objdump"
      - "/sbin/llvm-objdump"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/objdump"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/objdump"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/objdump"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/objdump"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/objdump"
      - "/Users/<USER>/Documents/node_modules/.bin/objdump"
      - "/Users/<USER>/node_modules/.bin/objdump"
      - "/Users/<USER>/.bin/objdump"
      - "/node_modules/.bin/objdump"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/objdump"
      - "/usr/local/bin/objdump"
      - "/System/Cryptexes/App/usr/bin/objdump"
    found: "/usr/bin/objdump"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-objcopy"
      - "objcopy"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/llvm-objcopy"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/llvm-objcopy"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/llvm-objcopy"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/llvm-objcopy"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/llvm-objcopy"
      - "/Users/<USER>/Documents/node_modules/.bin/llvm-objcopy"
      - "/Users/<USER>/node_modules/.bin/llvm-objcopy"
      - "/Users/<USER>/.bin/llvm-objcopy"
      - "/node_modules/.bin/llvm-objcopy"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/llvm-objcopy"
      - "/usr/local/bin/llvm-objcopy"
      - "/System/Cryptexes/App/usr/bin/llvm-objcopy"
      - "/usr/bin/llvm-objcopy"
      - "/bin/llvm-objcopy"
      - "/usr/sbin/llvm-objcopy"
      - "/sbin/llvm-objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/llvm-objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/llvm-objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/llvm-objcopy"
      - "/Library/Apple/usr/bin/llvm-objcopy"
      - "/Users/<USER>/.codeium/windsurf/bin/llvm-objcopy"
      - "/Users/<USER>/.deno/bin/llvm-objcopy"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/llvm-objcopy"
      - "/usr/local/sbin/llvm-objcopy"
      - "/opt/homebrew/bin/llvm-objcopy"
      - "/opt/homebrew/sbin/llvm-objcopy"
      - "/Users/<USER>/.local/bin/llvm-objcopy"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-objcopy"
      - "/bin/llvm-objcopy"
      - "/sbin/llvm-objcopy"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/objcopy"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/objcopy"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/objcopy"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/objcopy"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/objcopy"
      - "/Users/<USER>/Documents/node_modules/.bin/objcopy"
      - "/Users/<USER>/node_modules/.bin/objcopy"
      - "/Users/<USER>/.bin/objcopy"
      - "/node_modules/.bin/objcopy"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/objcopy"
      - "/usr/local/bin/objcopy"
      - "/System/Cryptexes/App/usr/bin/objcopy"
      - "/usr/bin/objcopy"
      - "/bin/objcopy"
      - "/usr/sbin/objcopy"
      - "/sbin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/objcopy"
      - "/Library/Apple/usr/bin/objcopy"
      - "/Users/<USER>/.codeium/windsurf/bin/objcopy"
      - "/Users/<USER>/.deno/bin/objcopy"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/objcopy"
      - "/usr/local/sbin/objcopy"
      - "/opt/homebrew/bin/objcopy"
      - "/opt/homebrew/sbin/objcopy"
      - "/Users/<USER>/.local/bin/objcopy"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/objcopy"
      - "/bin/objcopy"
      - "/sbin/objcopy"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-readelf"
      - "readelf"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/llvm-readelf"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/llvm-readelf"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/llvm-readelf"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/llvm-readelf"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/llvm-readelf"
      - "/Users/<USER>/Documents/node_modules/.bin/llvm-readelf"
      - "/Users/<USER>/node_modules/.bin/llvm-readelf"
      - "/Users/<USER>/.bin/llvm-readelf"
      - "/node_modules/.bin/llvm-readelf"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/llvm-readelf"
      - "/usr/local/bin/llvm-readelf"
      - "/System/Cryptexes/App/usr/bin/llvm-readelf"
      - "/usr/bin/llvm-readelf"
      - "/bin/llvm-readelf"
      - "/usr/sbin/llvm-readelf"
      - "/sbin/llvm-readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/llvm-readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/llvm-readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/llvm-readelf"
      - "/Library/Apple/usr/bin/llvm-readelf"
      - "/Users/<USER>/.codeium/windsurf/bin/llvm-readelf"
      - "/Users/<USER>/.deno/bin/llvm-readelf"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/llvm-readelf"
      - "/usr/local/sbin/llvm-readelf"
      - "/opt/homebrew/bin/llvm-readelf"
      - "/opt/homebrew/sbin/llvm-readelf"
      - "/Users/<USER>/.local/bin/llvm-readelf"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-readelf"
      - "/bin/llvm-readelf"
      - "/sbin/llvm-readelf"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/readelf"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/readelf"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/readelf"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/readelf"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/readelf"
      - "/Users/<USER>/Documents/node_modules/.bin/readelf"
      - "/Users/<USER>/node_modules/.bin/readelf"
      - "/Users/<USER>/.bin/readelf"
      - "/node_modules/.bin/readelf"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/readelf"
      - "/usr/local/bin/readelf"
      - "/System/Cryptexes/App/usr/bin/readelf"
      - "/usr/bin/readelf"
      - "/bin/readelf"
      - "/usr/sbin/readelf"
      - "/sbin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/readelf"
      - "/Library/Apple/usr/bin/readelf"
      - "/Users/<USER>/.codeium/windsurf/bin/readelf"
      - "/Users/<USER>/.deno/bin/readelf"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/readelf"
      - "/usr/local/sbin/readelf"
      - "/opt/homebrew/bin/readelf"
      - "/opt/homebrew/sbin/readelf"
      - "/Users/<USER>/.local/bin/readelf"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/readelf"
      - "/bin/readelf"
      - "/sbin/readelf"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-dlltool"
      - "dlltool"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/llvm-dlltool"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/llvm-dlltool"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/llvm-dlltool"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/llvm-dlltool"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/llvm-dlltool"
      - "/Users/<USER>/Documents/node_modules/.bin/llvm-dlltool"
      - "/Users/<USER>/node_modules/.bin/llvm-dlltool"
      - "/Users/<USER>/.bin/llvm-dlltool"
      - "/node_modules/.bin/llvm-dlltool"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/llvm-dlltool"
      - "/usr/local/bin/llvm-dlltool"
      - "/System/Cryptexes/App/usr/bin/llvm-dlltool"
      - "/usr/bin/llvm-dlltool"
      - "/bin/llvm-dlltool"
      - "/usr/sbin/llvm-dlltool"
      - "/sbin/llvm-dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/llvm-dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/llvm-dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/llvm-dlltool"
      - "/Library/Apple/usr/bin/llvm-dlltool"
      - "/Users/<USER>/.codeium/windsurf/bin/llvm-dlltool"
      - "/Users/<USER>/.deno/bin/llvm-dlltool"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/llvm-dlltool"
      - "/usr/local/sbin/llvm-dlltool"
      - "/opt/homebrew/bin/llvm-dlltool"
      - "/opt/homebrew/sbin/llvm-dlltool"
      - "/Users/<USER>/.local/bin/llvm-dlltool"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-dlltool"
      - "/bin/llvm-dlltool"
      - "/sbin/llvm-dlltool"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/dlltool"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/dlltool"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/dlltool"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/dlltool"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/dlltool"
      - "/Users/<USER>/Documents/node_modules/.bin/dlltool"
      - "/Users/<USER>/node_modules/.bin/dlltool"
      - "/Users/<USER>/.bin/dlltool"
      - "/node_modules/.bin/dlltool"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/System/Cryptexes/App/usr/bin/dlltool"
      - "/usr/bin/dlltool"
      - "/bin/dlltool"
      - "/usr/sbin/dlltool"
      - "/sbin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/dlltool"
      - "/Library/Apple/usr/bin/dlltool"
      - "/Users/<USER>/.codeium/windsurf/bin/dlltool"
      - "/Users/<USER>/.deno/bin/dlltool"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/dlltool"
      - "/usr/local/sbin/dlltool"
      - "/opt/homebrew/bin/dlltool"
      - "/opt/homebrew/sbin/dlltool"
      - "/Users/<USER>/.local/bin/dlltool"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/dlltool"
      - "/bin/dlltool"
      - "/sbin/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-addr2line"
      - "addr2line"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/llvm-addr2line"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/llvm-addr2line"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/llvm-addr2line"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/llvm-addr2line"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/llvm-addr2line"
      - "/Users/<USER>/Documents/node_modules/.bin/llvm-addr2line"
      - "/Users/<USER>/node_modules/.bin/llvm-addr2line"
      - "/Users/<USER>/.bin/llvm-addr2line"
      - "/node_modules/.bin/llvm-addr2line"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/llvm-addr2line"
      - "/usr/local/bin/llvm-addr2line"
      - "/System/Cryptexes/App/usr/bin/llvm-addr2line"
      - "/usr/bin/llvm-addr2line"
      - "/bin/llvm-addr2line"
      - "/usr/sbin/llvm-addr2line"
      - "/sbin/llvm-addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/llvm-addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/llvm-addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/llvm-addr2line"
      - "/Library/Apple/usr/bin/llvm-addr2line"
      - "/Users/<USER>/.codeium/windsurf/bin/llvm-addr2line"
      - "/Users/<USER>/.deno/bin/llvm-addr2line"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/llvm-addr2line"
      - "/usr/local/sbin/llvm-addr2line"
      - "/opt/homebrew/bin/llvm-addr2line"
      - "/opt/homebrew/sbin/llvm-addr2line"
      - "/Users/<USER>/.local/bin/llvm-addr2line"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-addr2line"
      - "/bin/llvm-addr2line"
      - "/sbin/llvm-addr2line"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/addr2line"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/addr2line"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/addr2line"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/addr2line"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/addr2line"
      - "/Users/<USER>/Documents/node_modules/.bin/addr2line"
      - "/Users/<USER>/node_modules/.bin/addr2line"
      - "/Users/<USER>/.bin/addr2line"
      - "/node_modules/.bin/addr2line"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/addr2line"
      - "/usr/local/bin/addr2line"
      - "/System/Cryptexes/App/usr/bin/addr2line"
      - "/usr/bin/addr2line"
      - "/bin/addr2line"
      - "/usr/sbin/addr2line"
      - "/sbin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/addr2line"
      - "/Library/Apple/usr/bin/addr2line"
      - "/Users/<USER>/.codeium/windsurf/bin/addr2line"
      - "/Users/<USER>/.deno/bin/addr2line"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/addr2line"
      - "/usr/local/sbin/addr2line"
      - "/opt/homebrew/bin/addr2line"
      - "/opt/homebrew/sbin/addr2line"
      - "/Users/<USER>/.local/bin/addr2line"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/addr2line"
      - "/bin/addr2line"
      - "/sbin/addr2line"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/tapi"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/tapi"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/tapi"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/tapi"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/tapi"
      - "/Users/<USER>/Documents/node_modules/.bin/tapi"
      - "/Users/<USER>/node_modules/.bin/tapi"
      - "/Users/<USER>/.bin/tapi"
      - "/node_modules/.bin/tapi"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/tapi"
      - "/usr/local/bin/tapi"
      - "/System/Cryptexes/App/usr/bin/tapi"
      - "/usr/bin/tapi"
      - "/bin/tapi"
      - "/usr/sbin/tapi"
      - "/sbin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/tapi"
      - "/Library/Apple/usr/bin/tapi"
      - "/Users/<USER>/.codeium/windsurf/bin/tapi"
      - "/Users/<USER>/.deno/bin/tapi"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/tapi"
      - "/usr/local/sbin/tapi"
      - "/opt/homebrew/bin/tapi"
      - "/opt/homebrew/sbin/tapi"
      - "/Users/<USER>/.local/bin/tapi"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/tapi"
      - "/bin/tapi"
      - "/sbin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake:50 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_CLANG_SCAN_DEPS"
    description: "`clang-scan-deps` dependency scanner"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "clang-scan-deps-22.0"
      - "clang-scan-deps-22"
      - "clang-scan-deps22"
      - "clang-scan-deps"
    candidate_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/"
      - "/Users/<USER>/Documents/node_modules/.bin/"
      - "/Users/<USER>/node_modules/.bin/"
      - "/Users/<USER>/.bin/"
      - "/node_modules/.bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/.deno/bin/"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/"
      - "/usr/local/sbin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
      - "/sbin/"
    searched_directories:
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/clang-scan-deps-22.0"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/clang-scan-deps-22.0"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/clang-scan-deps-22.0"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/clang-scan-deps-22.0"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/clang-scan-deps-22.0"
      - "/Users/<USER>/Documents/node_modules/.bin/clang-scan-deps-22.0"
      - "/Users/<USER>/node_modules/.bin/clang-scan-deps-22.0"
      - "/Users/<USER>/.bin/clang-scan-deps-22.0"
      - "/node_modules/.bin/clang-scan-deps-22.0"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/clang-scan-deps-22.0"
      - "/usr/local/bin/clang-scan-deps-22.0"
      - "/System/Cryptexes/App/usr/bin/clang-scan-deps-22.0"
      - "/usr/bin/clang-scan-deps-22.0"
      - "/bin/clang-scan-deps-22.0"
      - "/usr/sbin/clang-scan-deps-22.0"
      - "/sbin/clang-scan-deps-22.0"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/clang-scan-deps-22.0"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/clang-scan-deps-22.0"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/clang-scan-deps-22.0"
      - "/Library/Apple/usr/bin/clang-scan-deps-22.0"
      - "/Users/<USER>/.codeium/windsurf/bin/clang-scan-deps-22.0"
      - "/Users/<USER>/.deno/bin/clang-scan-deps-22.0"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/clang-scan-deps-22.0"
      - "/usr/local/sbin/clang-scan-deps-22.0"
      - "/opt/homebrew/bin/clang-scan-deps-22.0"
      - "/opt/homebrew/sbin/clang-scan-deps-22.0"
      - "/Users/<USER>/.local/bin/clang-scan-deps-22.0"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/clang-scan-deps-22.0"
      - "/bin/clang-scan-deps-22.0"
      - "/sbin/clang-scan-deps-22.0"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/clang-scan-deps-22"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/clang-scan-deps-22"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/clang-scan-deps-22"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/clang-scan-deps-22"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/clang-scan-deps-22"
      - "/Users/<USER>/Documents/node_modules/.bin/clang-scan-deps-22"
      - "/Users/<USER>/node_modules/.bin/clang-scan-deps-22"
      - "/Users/<USER>/.bin/clang-scan-deps-22"
      - "/node_modules/.bin/clang-scan-deps-22"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/clang-scan-deps-22"
      - "/usr/local/bin/clang-scan-deps-22"
      - "/System/Cryptexes/App/usr/bin/clang-scan-deps-22"
      - "/usr/bin/clang-scan-deps-22"
      - "/bin/clang-scan-deps-22"
      - "/usr/sbin/clang-scan-deps-22"
      - "/sbin/clang-scan-deps-22"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/clang-scan-deps-22"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/clang-scan-deps-22"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/clang-scan-deps-22"
      - "/Library/Apple/usr/bin/clang-scan-deps-22"
      - "/Users/<USER>/.codeium/windsurf/bin/clang-scan-deps-22"
      - "/Users/<USER>/.deno/bin/clang-scan-deps-22"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/clang-scan-deps-22"
      - "/usr/local/sbin/clang-scan-deps-22"
      - "/opt/homebrew/bin/clang-scan-deps-22"
      - "/opt/homebrew/sbin/clang-scan-deps-22"
      - "/Users/<USER>/.local/bin/clang-scan-deps-22"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/clang-scan-deps-22"
      - "/bin/clang-scan-deps-22"
      - "/sbin/clang-scan-deps-22"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/clang-scan-deps22"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/clang-scan-deps22"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/clang-scan-deps22"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/clang-scan-deps22"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/clang-scan-deps22"
      - "/Users/<USER>/Documents/node_modules/.bin/clang-scan-deps22"
      - "/Users/<USER>/node_modules/.bin/clang-scan-deps22"
      - "/Users/<USER>/.bin/clang-scan-deps22"
      - "/node_modules/.bin/clang-scan-deps22"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/clang-scan-deps22"
      - "/usr/local/bin/clang-scan-deps22"
      - "/System/Cryptexes/App/usr/bin/clang-scan-deps22"
      - "/usr/bin/clang-scan-deps22"
      - "/bin/clang-scan-deps22"
      - "/usr/sbin/clang-scan-deps22"
      - "/sbin/clang-scan-deps22"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/clang-scan-deps22"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/clang-scan-deps22"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/clang-scan-deps22"
      - "/Library/Apple/usr/bin/clang-scan-deps22"
      - "/Users/<USER>/.codeium/windsurf/bin/clang-scan-deps22"
      - "/Users/<USER>/.deno/bin/clang-scan-deps22"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/clang-scan-deps22"
      - "/usr/local/sbin/clang-scan-deps22"
      - "/opt/homebrew/bin/clang-scan-deps22"
      - "/opt/homebrew/sbin/clang-scan-deps22"
      - "/Users/<USER>/.local/bin/clang-scan-deps22"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/clang-scan-deps22"
      - "/bin/clang-scan-deps22"
      - "/sbin/clang-scan-deps22"
      - "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/clang-scan-deps"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin/clang-scan-deps"
      - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin/clang-scan-deps"
      - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin/clang-scan-deps"
      - "/Users/<USER>/Documents/Projects/node_modules/.bin/clang-scan-deps"
      - "/Users/<USER>/Documents/node_modules/.bin/clang-scan-deps"
      - "/Users/<USER>/node_modules/.bin/clang-scan-deps"
      - "/Users/<USER>/.bin/clang-scan-deps"
      - "/node_modules/.bin/clang-scan-deps"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin/clang-scan-deps"
      - "/usr/local/bin/clang-scan-deps"
      - "/System/Cryptexes/App/usr/bin/clang-scan-deps"
      - "/usr/bin/clang-scan-deps"
      - "/bin/clang-scan-deps"
      - "/usr/sbin/clang-scan-deps"
      - "/sbin/clang-scan-deps"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/clang-scan-deps"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/clang-scan-deps"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/clang-scan-deps"
      - "/Library/Apple/usr/bin/clang-scan-deps"
      - "/Users/<USER>/.codeium/windsurf/bin/clang-scan-deps"
      - "/Users/<USER>/.deno/bin/clang-scan-deps"
      - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/clang-scan-deps"
      - "/usr/local/sbin/clang-scan-deps"
      - "/opt/homebrew/bin/clang-scan-deps"
      - "/opt/homebrew/sbin/clang-scan-deps"
      - "/Users/<USER>/.local/bin/clang-scan-deps"
      - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand/clang-scan-deps"
      - "/bin/clang-scan-deps"
      - "/sbin/clang-scan-deps"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/SmartSpectra/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/Personal/node_modules/.bin"
        - "/Users/<USER>/Documents/Projects/node_modules/.bin"
        - "/Users/<USER>/Documents/node_modules/.bin"
        - "/Users/<USER>/node_modules/.bin"
        - "/Users/<USER>/.bin"
        - "/node_modules/.bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/.deno/bin"
        - "/Users/<USER>/.nvm/versions/node/v22.16.0/bin"
        - "/usr/local/sbin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/"
        - "/"
      CMAKE_FIND_ROOT_PATH: "/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot;/opt/homebrew/Cellar/emscripten/4.0.12/libexec/cache/sysroot"
...
