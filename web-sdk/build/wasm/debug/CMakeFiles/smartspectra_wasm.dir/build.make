# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug

# Include any dependencies generated for this target.
include CMakeFiles/smartspectra_wasm.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/smartspectra_wasm.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/smartspectra_wasm.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/smartspectra_wasm.dir/flags.make

CMakeFiles/smartspectra_wasm.dir/codegen:
.PHONY : CMakeFiles/smartspectra_wasm.dir/codegen

CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o: CMakeFiles/smartspectra_wasm.dir/flags.make
CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o: CMakeFiles/smartspectra_wasm.dir/includes_CXX.rsp
CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o: /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/smartspectra_wasm_minimal.cpp
CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o: CMakeFiles/smartspectra_wasm.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o"
	/opt/homebrew/Cellar/emscripten/4.0.12/libexec/em++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o -MF CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o.d -o CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o -c /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/smartspectra_wasm_minimal.cpp

CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.i"
	/opt/homebrew/Cellar/emscripten/4.0.12/libexec/em++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/smartspectra_wasm_minimal.cpp > CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.i

CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.s"
	/opt/homebrew/Cellar/emscripten/4.0.12/libexec/em++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/smartspectra_wasm_minimal.cpp -o CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.s

CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o: CMakeFiles/smartspectra_wasm.dir/flags.make
CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o: CMakeFiles/smartspectra_wasm.dir/includes_CXX.rsp
CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o: /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/bindings_minimal.cpp
CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o: CMakeFiles/smartspectra_wasm.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o"
	/opt/homebrew/Cellar/emscripten/4.0.12/libexec/em++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o -MF CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o.d -o CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o -c /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/bindings_minimal.cpp

CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.i"
	/opt/homebrew/Cellar/emscripten/4.0.12/libexec/em++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/bindings_minimal.cpp > CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.i

CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.s"
	/opt/homebrew/Cellar/emscripten/4.0.12/libexec/em++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/bindings_minimal.cpp -o CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.s

# Object files for target smartspectra_wasm
smartspectra_wasm_OBJECTS = \
"CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o" \
"CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o"

# External object files for target smartspectra_wasm
smartspectra_wasm_EXTERNAL_OBJECTS =

/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js: CMakeFiles/smartspectra_wasm.dir/smartspectra_wasm_minimal.cpp.o
/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js: CMakeFiles/smartspectra_wasm.dir/bindings_minimal.cpp.o
/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js: CMakeFiles/smartspectra_wasm.dir/build.make
/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js: CMakeFiles/smartspectra_wasm.dir/objects1.rsp
/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js: CMakeFiles/smartspectra_wasm.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/smartspectra_wasm.dir/link.txt --verbose=$(VERBOSE)
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "Copying WASM files to output directory"
	/opt/homebrew/bin/cmake -E copy /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/smartspectra_wasm.wasm /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/../../wasm/smartspectra_wasm.wasm
	/opt/homebrew/bin/cmake -E copy /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/smartspectra_wasm.js /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm/../../wasm/smartspectra_wasm.js

# Rule to build all files generated by this target.
CMakeFiles/smartspectra_wasm.dir/build: /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js
.PHONY : CMakeFiles/smartspectra_wasm.dir/build

CMakeFiles/smartspectra_wasm.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/smartspectra_wasm.dir/cmake_clean.cmake
.PHONY : CMakeFiles/smartspectra_wasm.dir/clean

CMakeFiles/smartspectra_wasm.dir/depend:
	cd /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/src/wasm /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/build/wasm/debug/CMakeFiles/smartspectra_wasm.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/smartspectra_wasm.dir/depend

