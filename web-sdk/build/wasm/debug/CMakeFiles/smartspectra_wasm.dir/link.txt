/opt/homebrew/Cellar/emscripten/4.0.12/libexec/em++  -sUSE_PTHREADS=0 -O1 -g3 -g  -sUSE_PTHREADS=0 -sALLOW_MEMORY_GROWTH=1 -sINITIAL_MEMORY=64MB -sMAXIMUM_MEMORY=512MB -sSTACK_SIZE=2MB -sEXPORTED_RUNTIME_METHODS=['ccall','cwrap'] -sEXPORTED_FUNCTIONS=['_malloc','_free'] -sMODULARIZE=1 -sEXPORT_NAME='SmartSpectraWASM' -sENVIRONMENT=web -sEXPORT_ES6=1 -sSINGLE_FILE=0 --bind -lembind -O1 -g3 -sASSERTIONS=1 -sSAFE_HEAP=1 -sSTACK_OVERFLOW_CHECK=1 -sDEMANGLE_SUPPORT=1 @CMakeFiles/smartspectra_wasm.dir/objects1.rsp -o /Users/<USER>/Documents/Projects/Personal/SmartSpectra/web-sdk/wasm/smartspectra_wasm.js
