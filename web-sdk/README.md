# SmartSpectra Web SDK

A server-side JavaScript SDK for integrating SmartSpectra's heart rate and respiration rate measurement capabilities into web applications using real-time video processing.

## Features

- **Real-time Processing**: Server-side processing using the actual C++ SmartSpectra algorithms
- **WebSocket Communication**: Real-time video frame transmission and result streaming
- **TypeScript Support**: Full TypeScript definitions for type safety
- **Modern JavaScript**: ES6+ modules with Promise-based and callback APIs
- **Browser Compatible**: Works in Chrome, Firefox, Safari, and Edge
- **API Key Authentication**: Simple authentication using API keys
- **Scalable Architecture**: Server-side processing supports multiple concurrent sessions
- **Comprehensive Documentation**: Auto-generated docs with interactive examples

## Installation

```bash
npm install @smartspectra/web-sdk
```

## Quick Start

```javascript
import { SmartSpectraWebSDK } from '@smartspectra/web-sdk';

// Initialize the SDK
await SmartSpectraWebSDK.initialize();
const sdk = SmartSpectraWebSDK.getInstance();

// Configure with your API key
sdk.configure({
  apiKey: 'YOUR_API_KEY',
  mode: 'spot',
  spotDuration: 30
});

// Start measurement
const stream = await sdk.requestCameraPermission();
await sdk.setVideoStream(stream);

await sdk.start({
  mode: 'spot',
  onResult: (metrics) => {
    console.log('Heart Rate:', metrics.pulse?.rate);
    console.log('Breathing Rate:', metrics.breathing?.rate);
  },
  onStatus: (status) => {
    console.log('Status:', status);
  }
});
```

## Documentation

- [API Reference](./docs/api/index.html)
- [Getting Started Guide](./docs/getting-started.md)
- [Examples](./examples/)
- [Browser Compatibility](./docs/browser-compatibility.md)

## Examples

- [Vanilla JavaScript](./examples/vanilla/)
- [React](./examples/react/)

## Development

See [DEVELOPMENT.md](./DEVELOPMENT.md) for build instructions and development setup.

## License

See [LICENSE](./LICENSE) for license information.
