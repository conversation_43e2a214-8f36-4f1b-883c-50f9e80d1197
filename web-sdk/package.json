{"name": "@smartspectra/web-sdk", "version": "1.0.0", "description": "WebAssembly-based JavaScript SDK for SmartSpectra heart rate and respiration rate measurement", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist/", "wasm/", "README.md", "LICENSE"], "scripts": {"build": "npm run build:wasm && npm run build:js", "build:wasm": "npm run build:wasm:debug && npm run build:wasm:release", "build:wasm:debug": "emcmake cmake -B build/wasm/debug -S src/wasm -DCMAKE_BUILD_TYPE=Debug && emmake make -C build/wasm/debug", "build:wasm:release": "emcmake cmake -B build/wasm/release -S src/wasm -DCMAKE_BUILD_TYPE=Release && emmake make -C build/wasm/release", "build:js": "rollup -c rollup.config.js", "build:types": "tsc --emitDeclarationOnly", "build:docs": "npm run build:docs:api && npm run build:docs:web", "build:docs:api": "typedoc src/js/index.ts --out docs/api", "build:docs:web": "node scripts/build-docs.js", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:browser": "playwright test", "lint": "eslint src/ --ext .ts,.js", "lint:fix": "eslint src/ --ext .ts,.js --fix", "clean": "rimraf dist/ build/ docs/api/", "dev": "rollup -c rollup.config.js --watch", "serve:examples": "http-server examples/ -p 8080 -c-1", "prepare": "npm run build"}, "keywords": ["smartspectra", "heart-rate", "respiration", "webassembly", "wasm", "computer-vision", "health-monitoring", "vital-signs"], "author": "Presage Technologies", "license": "SEE LICENSE IN LICENSE", "repository": {"type": "git", "url": "https://github.com/presagetech/smartspectra.git", "directory": "web-sdk"}, "bugs": {"url": "https://github.com/presagetech/smartspectra/issues"}, "homepage": "https://github.com/presagetech/smartspectra/tree/main/web-sdk#readme", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "http-server": "^14.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "playwright": "^1.40.0", "rimraf": "^5.0.0", "rollup": "^4.0.0", "rollup-plugin-copy": "^3.5.0", "ts-jest": "^29.0.0", "typedoc": "^0.25.0", "typescript": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.5.0"}, "engines": {"node": ">=16.0.0"}, "browserslist": ["Chrome >= 80", "Firefox >= 79", "Safari >= 14", "Edge >= 80"], "dependencies": {"tslib": "^2.8.1"}}