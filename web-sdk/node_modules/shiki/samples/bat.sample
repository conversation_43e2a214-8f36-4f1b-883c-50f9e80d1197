rem
rem  Alternate form of if-elseif-else structure with goto for else
rem  case.  That way, you can group code together in a "more logical"
rem  or "more natural" manner.
rem

if .%1 == .1 goto 1
if .%1 == .2 goto 2
goto else
:1
echo You selected 1
goto endif
:2
echo You selected 2
goto endif
:else
echo else (neither 1 nor 2)
goto endif
:endif

:: From https://github.com/Archive-projects/Batch-File-examples/blob/master/files/tf5.bat
