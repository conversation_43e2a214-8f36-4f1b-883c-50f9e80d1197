{"squadName": "Super hero squad", "homeTown": "Metro City", "formed": 2016, "secretBase": "Super tower", "active": true, "members": [{"name": "Molecule Man", "age": 29, "secretIdentity": "<PERSON>", "powers": ["Radiation resistance", "Turning tiny", "Radiation blast"]}, {"name": "<PERSON>", "age": 39, "secretIdentity": "<PERSON>", "powers": ["Million tonne punch", "Damage resistance", "Superhuman reflexes"]}, {"name": "Eternal Flame", "age": 1000000, "secretIdentity": "Unknown", "powers": ["Immortality", "Heat Immunity", "Inferno", "Teleportation", "Interdimensional travel"]}], "from": "https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Objects/JSON"}