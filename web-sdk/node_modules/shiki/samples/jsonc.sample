// A jsonc example document
{
 owner:{
  name:`komkom`
  dob: /* just some random dob */ `1975-01-25T12:00:00-02:00`
 }

 database:{ // our live db
  server:`***********`
  ports:[8001,8002,8003]
  connectionMax:5000
  enabled:true
 }

 servers:{ // a server
  alpha:{
   ip: /* is soon invalid */ `********`
   dc:`eqdc10`
  }

  beta:{
   ip:`********`
   dc:`eqdc10`
  }
 }

 clients:{
  data:[["gamma","delta"],[1,2]]
 }

 hosts:[alpha,omega]
}

// From https://github.com/komkom/jsonc
