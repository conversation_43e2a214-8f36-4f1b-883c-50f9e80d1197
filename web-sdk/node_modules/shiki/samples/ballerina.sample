import ballerina/io;

// This function definition has two parameters of type `int`. 
// `returns` clause specifies type of return value.
function add(int x, int y) returns int {

    int sum = x + y;
    // `return` statement returns a value.
    return sum;

}

public function main() {
    io:println(add(5, 11));
}
import ballerina/io;

// This function definition has two parameters of type `int`. 
// `returns` clause specifies type of return value.
function add(int x, int y) returns int {

    int sum = x + y;
    // `return` statement returns a value.
    return sum;

}

public function main() {
    io:println(add(5, 11));
}

// From https://ballerina.io/learn/by-example/functions