tell application "Address Book"

    set bDay<PERSON>ist to name of every person whose birth date is not missing value

    choose from list bDay<PERSON>ist with prompt "Whose birthday would you like?"

    if the result is not false then

        set aName to item 1 of the result

        set theBirthday to birth date of person named a<PERSON><PERSON>

        display dialog aN<PERSON> & "'s birthday is " & date string of theBirthday

    end if

end tell

-- From https://developer.apple.com/library/archive/documentation/AppleScript/Conceptual/AppleScriptLangGuide/reference/ASLR_cmds.html