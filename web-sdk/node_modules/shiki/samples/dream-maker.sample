/mob/Login()
    var/count = 0

    world << "Let's count until infinity!"

    // Infinite loop
    while (TRUE)
        count += 1

        if (count == 3)
            world << "three"

            // Skip the rest of this iteration
            continue

        world << "#[count]"

        if (count == 5)
            world << "OK, that's enough"

            // Exit this loop
            break

// From https://spacestation13.github.io/DMByExample/flow/loops.html
