{"name": "shiki", "version": "0.14.7", "description": "shiki", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/octref/shiki/tree/main/packages/shiki", "license": "MIT", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "unpkg": "dist/index.unpkg.iife.js", "jsdelivr": "dist/index.jsdelivr.iife.js", "keywords": ["shiki", "syntax-highlighter", "highlighter"], "files": ["dist", "languages", "themes", "samples"], "scripts": {"prepublishOnly": "npm run build", "build": "rollup -c", "watch": "rollup -c -w"}, "repository": {"type": "git", "url": "git+https://github.com/octref/shiki.git"}, "dependencies": {"ansi-sequence-parser": "^1.1.0", "jsonc-parser": "^3.2.0", "vscode-oniguruma": "^1.7.0", "vscode-textmate": "^8.0.0"}, "devDependencies": {"@types/node": "^18.11.17"}, "browser": {"fs": false, "path": false}, "gitHead": "dff40b62c619ba8064a5ceea215dbd98434c082a"}