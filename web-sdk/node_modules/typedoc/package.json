{"name": "typedoc", "description": "Create api documentation for TypeScript projects.", "version": "0.25.13", "homepage": "https://typedoc.org", "exports": {".": "./dist/index.js", "./tsdoc.json": "./tsdoc.json", "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "bin": {"typedoc": "bin/typedoc"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git://github.com/TypeStrong/TypeDoc.git"}, "bugs": {"url": "https://github.com/TypeStrong/TypeDoc/issues"}, "engines": {"node": ">= 16"}, "dependencies": {"lunr": "^2.3.9", "marked": "^4.3.0", "minimatch": "^9.0.3", "shiki": "^0.14.7"}, "peerDependencies": {"typescript": "4.6.x || 4.7.x || 4.8.x || 4.9.x || 5.0.x || 5.1.x || 5.2.x || 5.3.x || 5.4.x"}, "devDependencies": {"@types/lunr": "^2.3.7", "@types/marked": "^4.0.8", "@types/mocha": "^10.0.6", "@types/node": "16", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "@typestrong/fs-fixture-builder": "github:TypeStrong/fs-fixture-builder#8abd1494280116ff5318dde2c50ad01e1663790c", "c8": "^9.1.0", "esbuild": "^0.20.2", "eslint": "^8.57.0", "mocha": "^10.4.0", "prettier": "3.0.3", "puppeteer": "^13.5.2", "ts-node": "^10.9.2", "typescript": "5.4.3"}, "files": ["/bin", "!*.map", "/dist", "!/dist/tmp", "!/dist/.tsbuildinfo", "!/dist/test", "/LICENSE", "/static", "/tsdoc.json"], "scripts": {"test": "mocha --config .config/mocha.fast.json", "test:cov": "c8 mocha --config .config/mocha.fast.json", "doc:c": "node bin/typedoc --tsconfig src/test/converter/tsconfig.json", "doc:cd": "node --inspect-brk bin/typedoc --tsconfig src/test/converter/tsconfig.json", "doc:c2": "node bin/typedoc --options src/test/converter2 --tsconfig src/test/converter2/tsconfig.json", "doc:c2d": "node --inspect-brk bin/typedoc --options src/test/converter2 --tsconfig src/test/converter2/tsconfig.json", "example": "cd example && node ../bin/typedoc", "test:full": "c8 mocha --config .config/mocha.full.json", "test:visual": "ts-node ./src/test/capture-screenshots.ts && ./scripts/compare_screenshots.sh", "test:visual:accept": "node scripts/accept_visual_regression.js", "rebuild_specs": "node scripts/rebuild_specs.js", "build": "npm run build:tsc && npm run build:themes", "build:tsc": "tsc --project .", "build:themes": "node scripts/build_themes.js", "build:prod": "npm run build:prod:tsc && npm run build:themes", "build:prod:tsc": "tsc --project . --sourceMap false --declarationMap false", "lint": "eslint . && npm run prettier -- --check .", "prettier": "prettier --config .config/.prettierrc.json --ignore-path .config/.prettierignore", "prepublishOnly": "node scripts/set_strict.js false && npm run build:prod && npm test", "postpublish": "node scripts/set_strict.js true"}, "keywords": ["typescript", "documentation", "generator"], "c8": {"extension": [".ts", ".tsx"], "reporter": ["html-spa", "text-summary"], "exclude": ["**/*.d.ts", "src/test", "tmp"]}}