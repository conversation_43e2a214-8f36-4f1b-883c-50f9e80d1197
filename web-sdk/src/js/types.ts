/**
 * TypeScript definitions for SmartSpectra Server-Side JS SDK
 * Aligned with protobuf definitions and native SDK interfaces
 */

// Core operation types - aligned with Swift/Android naming
export enum SmartSpectraMode {
  SPOT = 'spot',
  CONTINUOUS = 'continuous'
}

export type QualityLevel = 'good' | 'fair' | 'poor';
export type ErrorCategory = 'initialization' | 'configuration' | 'runtime' | 'resource';

// Status codes matching C++ physiology::StatusCode enum exactly
export enum StatusCode {
  OK = 'OK',
  NO_FACES_FOUND = 'NO_FACES_FOUND',
  MORE_THAN_ONE_FACE_FOUND = 'MORE_THAN_ONE_FACE_FOUND',
  FACE_NOT_CENTERED = 'FACE_NOT_CENTERED',
  FACE_TOO_BIG_OR_TOO_SMALL = 'FACE_TOO_BIG_OR_TOO_SMALL',
  IMAGE_TOO_DARK = 'IMAGE_TOO_DARK',
  IMAGE_TOO_BRIGHT = 'IMAGE_TOO_BRIGHT',
  CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING = 'CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING',
  PROCESSING_NOT_STARTED = 'PROCESSING_NOT_STARTED'
}

// Configuration interface aligned with C++ Settings structures
export interface SmartSpectraConfig {
  apiKey?: string;
  mode: SmartSpectraMode;
  spotDuration?: number; // 20..120 seconds (maps to C++ spot_duration_s)
  bufferDuration?: number; // maps to C++ preprocessed_data_buffer_duration_s
  serverUrl?: string;
  debug?: boolean;

  // Video source settings (handled in JavaScript, not passed to C++)
  cameraPosition?: 'front' | 'back';
  videoConstraints?: MediaTrackConstraints;

  // Processing settings (maps to C++ GeneralSettings)
  scaleInput?: boolean; // maps to C++ scale_input
  enablePhasicBP?: boolean; // maps to C++ enable_phasic_bp
  enableDenseFacemeshPoints?: boolean; // maps to C++ enable_dense_facemesh_points
  useFullRangeFaceDetection?: boolean; // maps to C++ use_full_range_face_detection
  enableEdgeMetrics?: boolean; // maps to C++ enable_edge_metrics
  verbosityLevel?: number; // maps to C++ verbosity_level
}

// Rich metrics data structure aligned with protobuf definitions
export interface MetricsBuffer {
  pulse?: Pulse;
  breathing?: Breathing;
  bloodPressure?: BloodPressure;
  face?: Face;
  metadata?: Metadata;
}

export interface Pulse {
  rate: MeasurementWithConfidence[];
  trace: Measurement[];
  pulseRespirationQuotient: Measurement[];
  strict?: Strict;
}

export interface Breathing {
  rate: MeasurementWithConfidence[];
  upperTrace: Measurement[];
  lowerTrace: Measurement[];
  amplitude: Measurement[];
  apnea: DetectionStatus[];
  respiratoryLineLength: Measurement[];
  baseline: Measurement[];
  inhaleExhaleRatio: Measurement[];
  strict?: Strict;
}

export interface BloodPressure {
  phasic: MeasurementWithConfidence[];
}

export interface Face {
  blinking: DetectionStatus[];
  talking: DetectionStatus[];
  landmarks: Landmarks[];
}

export interface Landmarks {
  time: number;
  value: Point2D[];
  stable: boolean;
}

export interface Point2D {
  x: number;
  y: number;
}

export interface Metadata {
  id: string;
  uploadTimestamp: string;
  apiVersion: string;
  sentAtS: number;
  frameTimestamp: number;
  frameCount: number;
}

export interface Measurement {
  time: number;
  value: number;
  stable: boolean;
}

export interface MeasurementWithConfidence extends Measurement {
  confidence: number;
}

export interface DetectionStatus {
  time: number;
  detected: boolean;
  stable: boolean;
}

export interface Strict {
  value: number;
}

export interface ModeState {
  mode: SmartSpectraMode;
  progress: number; // 0.0..1.0 for spot, -1 for continuous
  frameCount: number;
  duration: number; // elapsed time in seconds
  quality: QualityLevel;
  status: StatusCode;
  isStable: boolean;
}

export interface SmartSpectraCallbacks {
  onResult?: (result: MetricsBuffer) => void;
  onStatus?: (status: StatusCode) => void;
  onError?: (error: SmartSpectraError) => void;
  onModeComplete?: (mode: SmartSpectraMode, results: MetricsBuffer[]) => void;
  onMeshPointsUpdate?: (points: Point2D[]) => void; // For consistency with native SDKs
  onFrameSentThrough?: (frameSent: boolean, timestamp: number) => void; // From C++ BackgroundContainer
}

export interface StartOptions {
  mode: SmartSpectraMode;
  duration?: number;
  onResult?: (result: MetricsBuffer) => void;
  onStatus?: (status: StatusCode) => void;
  onError?: (error: SmartSpectraError) => void;
}

// Strongly-typed error codes enum
export enum ErrorCode {
  SERVER_CONNECTION_FAILED = 'SERVER_CONNECTION_FAILED',
  CAMERA_ACCESS_DENIED = 'CAMERA_ACCESS_DENIED',
  INVALID_API_KEY = 'INVALID_API_KEY',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PROCESSING_FAILED = 'PROCESSING_FAILED',
  MEMORY_ERROR = 'MEMORY_ERROR',
  INVALID_CONFIG = 'INVALID_CONFIG',
  NOT_INITIALIZED = 'NOT_INITIALIZED',
  ALREADY_RUNNING = 'ALREADY_RUNNING',
  NOT_RUNNING = 'NOT_RUNNING',
  UNSUPPORTED_BROWSER = 'UNSUPPORTED_BROWSER'
}

// Comprehensive error class with proper typing
export class SmartSpectraError extends Error {
  constructor(
    message: string,
    public code: ErrorCode,
    public category: ErrorCategory,
    public recoverable: boolean = false,
    public cause?: Error
  ) {
    super(message);
    this.name = 'SmartSpectraError';

    // Set the cause if provided (for newer browsers)
    if (cause && 'cause' in Error.prototype) {
      (this as any).cause = cause;
    }
  }
}

// Error reporting and recovery
export interface ErrorReport {
  error: SmartSpectraError;
  timestamp: number;
  context: Record<string, any>;
  recovery: {
    canRecover: boolean;
    recoveryAction?: string;
    attempts: number;
  };
}

// Browser support detection
export interface BrowserSupport {
  webSocket: boolean;
  getUserMedia: boolean;
  canvas: boolean;
  fetch: boolean;
  overall: boolean;
  missing: string[];
}

// Server connection options
export interface ServerConnectionOptions {
  serverUrl?: string;
  debug?: boolean;
  timeout?: number;
  retryAttempts?: number;
}

// Video frame data model
export interface VideoFrame {
  pixelData: Uint8Array;
  width: number;
  height: number;
  timestamp: number;
  format: 'RGB' | 'RGBA' | 'BGR';
}

// Maps directly to C++ Settings structures for server communication
export interface ServerConfig {
  mode: 'spot' | 'continuous'; // Maps to C++ OperationMode
  spot_duration_s?: number; // Maps to C++ SpotSettings::spot_duration_s (20-120 seconds)
  buffer_duration_s?: number; // Maps to C++ ContinuousSettings::preprocessed_data_buffer_duration_s
  api_key: string; // Maps to C++ RestSettings::api_key

  // GeneralSettings mappings
  scale_input?: boolean; // Maps to C++ GeneralSettings::scale_input
  enable_phasic_bp?: boolean; // Maps to C++ GeneralSettings::enable_phasic_bp
  enable_dense_facemesh_points?: boolean; // Maps to C++ GeneralSettings::enable_dense_facemesh_points
  use_full_range_face_detection?: boolean; // Maps to C++ GeneralSettings::use_full_range_face_detection
  enable_edge_metrics?: boolean; // Maps to C++ GeneralSettings::enable_edge_metrics
  verbosity_level?: number; // Maps to C++ GeneralSettings::verbosity_level
}
