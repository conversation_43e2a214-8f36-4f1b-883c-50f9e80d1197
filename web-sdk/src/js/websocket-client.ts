/**
 * WebSocket Client for SmartSpectra Web SDK
 * Handles real-time communication with the SmartSpectra server
 */

import { SmartSpectraError, ErrorCode, MetricsBuffer, VideoFrame } from './types';

export interface WebSocketMessage {
  type: 'frame' | 'start' | 'stop' | 'config' | 'status' | 'metrics' | 'error';
  sessionId?: string;
  data?: any;
  timestamp: number;
}

export interface WebSocketClientOptions {
  url: string;
  sessionId: string;
  onMetrics?: (metrics: MetricsBuffer) => void;
  onStatus?: (status: any) => void;
  onError?: (error: SmartSpectraError) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private options: WebSocketClientOptions;
  private connected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private reconnectTimer: number | null = null;

  constructor(options: WebSocketClientOptions) {
    this.options = options;
  }

  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.options.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.connected = true;
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;
          
          if (this.options.onConnect) {
            this.options.onConnect();
          }
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.connected = false;
          
          if (this.options.onDisconnect) {
            this.options.onDisconnect();
          }

          // Attempt to reconnect if not a clean close
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          
          if (!this.connected) {
            reject(new SmartSpectraError(
              'Failed to connect to server',
              ErrorCode.SERVER_CONNECTION_FAILED,
              'runtime'
            ));
          }
        };

      } catch (error) {
        reject(new SmartSpectraError(
          'WebSocket connection failed',
          ErrorCode.SERVER_CONNECTION_FAILED,
          'runtime',
          false,
          error instanceof Error ? error : new Error(String(error))
        ));
      }
    });
  }

  public disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.connected = false;
  }

  public isConnected(): boolean {
    return this.connected && this.ws?.readyState === WebSocket.OPEN;
  }

  public sendMessage(message: WebSocketMessage): void {
    if (!this.isConnected()) {
      throw new SmartSpectraError(
        'WebSocket not connected',
        ErrorCode.SERVER_CONNECTION_FAILED,
        'runtime'
      );
    }

    try {
      this.ws!.send(JSON.stringify(message));
    } catch (error) {
      throw new SmartSpectraError(
        'Failed to send WebSocket message',
        ErrorCode.NETWORK_ERROR,
        'runtime',
        true,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  public sendFrame(frame: VideoFrame): void {
    if (!this.isConnected()) {
      throw new SmartSpectraError(
        'WebSocket not connected',
        ErrorCode.SERVER_CONNECTION_FAILED,
        'runtime'
      );
    }

    try {
      // Create binary frame message
      // Format: [width:4][height:4][timestamp:8][pixel_data]
      const headerSize = 16;
      const frameBuffer = new ArrayBuffer(headerSize + frame.pixelData.length);
      const view = new DataView(frameBuffer);
      
      // Write header
      view.setUint32(0, frame.width, true); // little endian
      view.setUint32(4, frame.height, true);
      view.setFloat64(8, frame.timestamp, true);
      
      // Write pixel data
      const pixelArray = new Uint8Array(frameBuffer, headerSize);
      pixelArray.set(frame.pixelData);
      
      this.ws!.send(frameBuffer);
    } catch (error) {
      throw new SmartSpectraError(
        'Failed to send video frame',
        ErrorCode.NETWORK_ERROR,
        'runtime',
        true,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  public startProcessing(): void {
    this.sendMessage({
      type: 'start',
      sessionId: this.options.sessionId,
      timestamp: Date.now()
    });
  }

  public stopProcessing(): void {
    this.sendMessage({
      type: 'stop',
      sessionId: this.options.sessionId,
      timestamp: Date.now()
    });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'metrics':
          if (this.options.onMetrics && message.data) {
            this.options.onMetrics(message.data);
          }
          break;
          
        case 'status':
          if (this.options.onStatus && message.data) {
            this.options.onStatus(message.data);
          }
          break;
          
        case 'error':
          if (this.options.onError && message.data) {
            const error = new SmartSpectraError(
              message.data.message || 'Server error',
              message.data.code || ErrorCode.PROCESSING_FAILED,
              'runtime'
            );
            this.options.onError(error);
          }
          break;
          
        default:
          console.warn('Unknown WebSocket message type:', message.type);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
      
      if (this.options.onError) {
        this.options.onError(new SmartSpectraError(
          'Failed to parse server message',
          ErrorCode.PROCESSING_FAILED,
          'runtime'
        ));
      }
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      return; // Already scheduled
    }

    this.reconnectAttempts++;
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);

    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectTimer = null;
      this.connect().catch((error) => {
        console.error('Reconnect failed:', error);
        
        // Exponential backoff
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000); // Max 30 seconds
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        } else {
          console.error('Max reconnect attempts reached');
          if (this.options.onError) {
            this.options.onError(new SmartSpectraError(
              'Connection lost and unable to reconnect',
              ErrorCode.SERVER_CONNECTION_FAILED,
              'runtime'
            ));
          }
        }
      });
    }, this.reconnectDelay);
  }

  public static checkWebSocketSupport(): { supported: boolean; missing: string[] } {
    const missing: string[] = [];

    if (!window.WebSocket) {
      missing.push('WebSocket');
    }

    if (!window.ArrayBuffer) {
      missing.push('ArrayBuffer');
    }

    if (!window.DataView) {
      missing.push('DataView');
    }

    return {
      supported: missing.length === 0,
      missing
    };
  }
}
