import {
  SmartSpectraConfig,
  SmartSpectraMode,
  StatusCode,
  MetricsBuffer,
  StartOptions,
  ModeState,
  Point2D,
  SmartSpectraError,
  ErrorCode,
  ErrorReport,
  BrowserSupport,
  ServerConnectionOptions,
  ServerConfig
} from './types';
import { VideoCaptureManager } from './video-capture-manager';
import { NetworkClient } from './network-client';
import { WebSocketClient } from './websocket-client';

/**
 * Main SmartSpectra Web SDK class
 * Provides a high-level JavaScript API for heart rate and respiration measurement
 */
export class SmartSpectraWebSDK {
  private static instance: SmartSpectraWebSDK | null = null;
  private static initialized = false;

  private videoCaptureManager: VideoCaptureManager;
  private networkClient: NetworkClient;
  private webSocketClient: WebSocketClient | null = null;
  private config: SmartSpectraConfig | null = null;
  private currentMode: SmartSpectraMode | null = null;
  private isRunning = false;
  private errorHistory: ErrorReport[] = [];
  private errorListeners: ((report: ErrorReport) => void)[] = [];
  private metricsBuffer: MetricsBuffer | null = null;
  private meshPoints: Point2D[] = [];
  private denseMeshPoints: Point2D[] = [];
  private sessionId: string | null = null;

  private constructor() {
    this.videoCaptureManager = new VideoCaptureManager();
    this.networkClient = new NetworkClient();
  }

  /**
   * Static initialization - checks browser support and server connectivity
   */
  static async initialize(options?: ServerConnectionOptions): Promise<void> {
    if (SmartSpectraWebSDK.initialized) {
      return;
    }

    try {
      // Check browser support
      const support = SmartSpectraWebSDK.getBrowserSupport();
      if (!support.overall) {
        throw new SmartSpectraError(
          `Browser not supported. Missing: ${support.missing.join(', ')}`,
          ErrorCode.UNSUPPORTED_BROWSER,
          'initialization'
        );
      }

      // Test server connectivity if URL provided
      if (options?.serverUrl) {
        const networkClient = new NetworkClient();
        networkClient.setBaseUrl(options.serverUrl);

        try {
          await networkClient.getApiInfo();
        } catch (error) {
          console.warn('Server connectivity test failed, but continuing initialization');
        }
      }

      SmartSpectraWebSDK.initialized = true;

    } catch (error) {
      throw new SmartSpectraError(
        'Failed to initialize SDK',
        ErrorCode.SERVER_CONNECTION_FAILED,
        'initialization',
        false,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Get singleton instance
   */
  static getInstance(): SmartSpectraWebSDK {
    if (!SmartSpectraWebSDK.initialized) {
      throw new SmartSpectraError(
        'SDK not initialized. Call SmartSpectraWebSDK.initialize() first',
        ErrorCode.NOT_INITIALIZED,
        'initialization'
      );
    }

    if (!SmartSpectraWebSDK.instance) {
      SmartSpectraWebSDK.instance = new SmartSpectraWebSDK();
    }

    return SmartSpectraWebSDK.instance;
  }

  /**
   * Get SDK version
   */
  static getVersion(): string {
    return '1.0.0'; // TODO: Get from package.json
  }

  /**
   * Check if browser is supported
   */
  static isSupported(): boolean {
    return SmartSpectraWebSDK.getBrowserSupport().overall;
  }

  /**
   * Get detailed browser support information
   */
  static getBrowserSupport(): BrowserSupport {
    const missing: string[] = [];

    // Check WebSocket support
    if (!window.WebSocket) {
      missing.push('WebSocket');
    }

    // Check video capture support
    const videoSupport = VideoCaptureManager.checkBrowserSupport();
    if (!videoSupport.supported) {
      missing.push(...videoSupport.missing);
    }

    // Check network support
    const networkSupport = NetworkClient.checkNetworkSupport();
    if (!networkSupport.supported) {
      missing.push(...networkSupport.missing);
    }

    // Check other required APIs
    if (!window.performance?.now) {
      missing.push('Performance API');
    }

    if (!window.requestAnimationFrame) {
      missing.push('RequestAnimationFrame');
    }

    return {
      webSocket: !!window.WebSocket,
      getUserMedia: !!navigator.mediaDevices?.getUserMedia,
      canvas: !!HTMLCanvasElement.prototype.getContext,
      fetch: !!window.fetch,
      overall: missing.length === 0,
      missing
    };
  }

  /**
   * Unload the SDK and cleanup resources
   */
  static unload(): void {
    if (SmartSpectraWebSDK.instance) {
      SmartSpectraWebSDK.instance.dispose();
      SmartSpectraWebSDK.instance = null;
    }
    SmartSpectraWebSDK.initialized = false;
  }

  /**
   * Configure the SDK
   */
  configure(config: SmartSpectraConfig): void {
    this.config = { ...config };

    // Configure network client
    if (config.apiKey) {
      this.networkClient.setApiKey(config.apiKey);
    }

    // Configure server URL if provided
    if (config.serverUrl) {
      this.networkClient.setBaseUrl(config.serverUrl);
    }
  }

  /**
   * Individual setter methods (like Swift/Android)
   */
  setApiKey(apiKey: string): void {
    if (!this.config) {
      this.config = { mode: SmartSpectraMode.SPOT };
    }
    this.config.apiKey = apiKey;
    this.networkClient.setApiKey(apiKey);
  }

  setMeasurementDuration(duration: number): void {
    if (!this.config) {
      this.config = { mode: SmartSpectraMode.SPOT };
    }
    // Clamp duration to valid range (20-120 seconds)
    this.config.spotDuration = Math.max(20, Math.min(120, duration));
  }

  setSmartSpectraMode(mode: SmartSpectraMode): void {
    if (!this.config) {
      this.config = { mode };
    } else {
      this.config.mode = mode;
    }
  }

  setCameraPosition(position: 'front' | 'back'): void {
    if (!this.config) {
      this.config = { mode: SmartSpectraMode.SPOT };
    }
    this.config.cameraPosition = position;
  }

  /**
   * Video handling methods
   */
  async setVideoElement(videoElement: HTMLVideoElement): Promise<void> {
    await this.videoCaptureManager.setVideoElement(videoElement);
  }

  async setVideoStream(stream: MediaStream): Promise<void> {
    await this.videoCaptureManager.setVideoStream(stream);
  }

  /**
   * Request camera permission
   */
  async requestCameraPermission(): Promise<MediaStream> {
    const constraints = this.config?.videoConstraints || {};
    if (this.config?.cameraPosition) {
      constraints.facingMode = this.config.cameraPosition === 'front' ? 'user' : 'environment';
    }
    return await this.videoCaptureManager.requestCameraPermission(constraints);
  }

  /**
   * Start measurement
   */
  async start(options: StartOptions): Promise<MetricsBuffer | void> {
    if (this.isRunning) {
      throw new SmartSpectraError(
        'Already running',
        ErrorCode.ALREADY_RUNNING,
        'runtime'
      );
    }

    if (!this.config?.apiKey) {
      throw new SmartSpectraError(
        'API key not configured',
        ErrorCode.INVALID_API_KEY,
        'configuration'
      );
    }

    try {
      this.currentMode = options.mode;

      // Create server session
      await this.createServerSession(options);

      // Setup WebSocket connection
      await this.setupWebSocketConnection(options);

      // Start video capture and processing loop
      this.startProcessingLoop();

      this.isRunning = true;

      // For spot mode, return a promise that resolves when complete
      if (options.mode === SmartSpectraMode.SPOT) {
        return new Promise((resolve, reject) => {
          const originalOnResult = options.onResult;
          options.onResult = (result: MetricsBuffer) => {
            if (originalOnResult) originalOnResult(result);
            resolve(result);
          };

          const originalOnError = options.onError;
          options.onError = (error: SmartSpectraError) => {
            if (originalOnError) originalOnError(error);
            reject(error);
          };
        });
      }

    } catch (error) {
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * Stop measurement
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      // Stop WebSocket processing
      if (this.webSocketClient) {
        this.webSocketClient.stopProcessing();
        this.webSocketClient.disconnect();
        this.webSocketClient = null;
      }

      // Stop video capture
      this.videoCaptureManager.stopCapture();

      // Destroy server session
      if (this.sessionId) {
        await this.destroyServerSession();
      }

      this.isRunning = false;
      this.currentMode = null;
      this.sessionId = null;

    } catch (error) {
      throw new SmartSpectraError(
        'Failed to stop measurement',
        ErrorCode.PROCESSING_FAILED,
        'runtime',
        false,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * State management methods
   */
  getCurrentMode(): SmartSpectraMode | null {
    return this.currentMode;
  }

  getModeProgress(): number {
    // TODO: Implement progress tracking
    return this.currentMode === SmartSpectraMode.CONTINUOUS ? -1 : 0;
  }

  getModeState(): ModeState {
    return {
      mode: this.currentMode || SmartSpectraMode.SPOT,
      progress: this.getModeProgress(),
      frameCount: 0, // TODO: Track frame count
      duration: 0, // TODO: Track duration
      quality: 'good', // TODO: Implement quality assessment
      status: this.isRunning ? StatusCode.OK : StatusCode.PROCESSING_NOT_STARTED,
      isStable: false // TODO: Implement stability tracking
    };
  }

  /**
   * Property-based access (like Swift/Android)
   */
  get metricsBuffer(): MetricsBuffer | null {
    return this.metricsBuffer;
  }

  get meshPoints(): Point2D[] {
    return this.meshPoints;
  }

  get denseMeshPoints(): Point2D[] {
    return this.denseMeshPoints;
  }

  /**
   * Utility methods
   */
  isInitialized(): boolean {
    return this.config !== null;
  }

  isRunning(): boolean {
    return this.isRunning;
  }

  getVersion(): string {
    return SmartSpectraWebSDK.getVersion();
  }

  /**
   * Error handling methods
   */
  getLastError(): string {
    return this.errorHistory.length > 0 ? this.errorHistory[this.errorHistory.length - 1].error.message : '';
  }

  addErrorListener(listener: (report: ErrorReport) => void): void {
    this.errorListeners.push(listener);
  }

  removeErrorListener(listener: (report: ErrorReport) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  getErrorHistory(): ErrorReport[] {
    return [...this.errorHistory];
  }

  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * Cleanup and disposal
   */
  async dispose(): Promise<void> {
    await this.stop();
    this.videoCaptureManager.cleanup();

    if (this.webSocketClient) {
      this.webSocketClient.disconnect();
      this.webSocketClient = null;
    }

    this.config = null;
    this.metricsBuffer = null;
    this.meshPoints = [];
    this.denseMeshPoints = [];
    this.errorHistory = [];
    this.errorListeners = [];
    this.sessionId = null;
  }

  /**
   * Private methods
   */
  private async createServerSession(options: StartOptions): Promise<void> {
    if (!this.config) {
      throw new SmartSpectraError(
        'SDK not configured',
        ErrorCode.NOT_INITIALIZED,
        'configuration'
      );
    }

    const serverConfig: ServerConfig = {
      mode: options.mode,
      api_key: this.config.apiKey!,
      spot_duration_s: options.duration || this.config.spotDuration || 30,
      buffer_duration_s: this.config.bufferDuration,
      scale_input: this.config.scaleInput,
      enable_phasic_bp: this.config.enablePhasicBP,
      enable_dense_facemesh_points: this.config.enableDenseFacemeshPoints,
      use_full_range_face_detection: this.config.useFullRangeFaceDetection,
      enable_edge_metrics: this.config.enableEdgeMetrics,
      verbosity_level: this.config.verbosityLevel
    };

    try {
      // Create session via REST API
      const baseUrl = this.config.serverUrl || 'http://localhost:8080';
      const response = await fetch(`${baseUrl}/api/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify({
          mode: options.mode,
          config: serverConfig
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.sessionId = result.sessionId;
    } catch (error) {
      throw new SmartSpectraError(
        'Failed to create server session',
        ErrorCode.SERVER_CONNECTION_FAILED,
        'runtime',
        true,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  private async setupWebSocketConnection(options: StartOptions): Promise<void> {
    if (!this.sessionId || !this.config) {
      throw new SmartSpectraError(
        'Session not created or SDK not configured',
        ErrorCode.NOT_INITIALIZED,
        'runtime'
      );
    }

    const serverUrl = this.config.serverUrl || 'ws://localhost:8081';
    const wsUrl = `${serverUrl}/ws/${this.sessionId}`;

    this.webSocketClient = new WebSocketClient({
      url: wsUrl,
      sessionId: this.sessionId,
      onMetrics: (metrics: MetricsBuffer) => {
        this.metricsBuffer = metrics;
        if (options.onResult) {
          options.onResult(metrics);
        }
      },
      onStatus: (status: any) => {
        if (options.onStatus) {
          options.onStatus(status.status);
        }
      },
      onError: (error: SmartSpectraError) => {
        this.handleError(error);
        if (options.onError) {
          options.onError(error);
        }
      },
      onConnect: () => {
        console.log('WebSocket connected, starting processing');
        if (this.webSocketClient) {
          this.webSocketClient.startProcessing();
        }
      },
      onDisconnect: () => {
        console.log('WebSocket disconnected');
      }
    });

    await this.webSocketClient.connect();
  }

  private async destroyServerSession(): Promise<void> {
    if (!this.sessionId) {
      return;
    }

    try {
      const url = `${this.networkClient.baseUrl || 'http://localhost:8080'}/api/sessions/${this.sessionId}`;
      await fetch(url, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.config?.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.warn('Failed to destroy server session:', error);
    }
  }



  private startProcessingLoop(): void {
    this.videoCaptureManager.startCapture();
    this.processFrameLoop();
  }

  private processFrameLoop(): void {
    if (!this.isRunning || !this.webSocketClient) {
      return;
    }

    try {
      const frame = this.videoCaptureManager.extractPixelData();

      // Send frame via WebSocket
      this.webSocketClient.sendFrame(frame);

    } catch (error) {
      this.handleError(new SmartSpectraError(
        'Frame processing failed',
        ErrorCode.PROCESSING_FAILED,
        'runtime',
        true,
        error instanceof Error ? error : new Error(String(error))
      ));
    }

    // Continue processing loop
    if (this.isRunning) {
      requestAnimationFrame(() => this.processFrameLoop());
    }
  }

  private getStatusFromCode(code: number): StatusCode {
    const statusMap: Record<number, StatusCode> = {
      0: StatusCode.OK,
      1: StatusCode.NO_FACES_FOUND,
      2: StatusCode.MORE_THAN_ONE_FACE_FOUND,
      3: StatusCode.FACE_NOT_CENTERED,
      4: StatusCode.FACE_TOO_BIG_OR_TOO_SMALL,
      5: StatusCode.IMAGE_TOO_DARK,
      6: StatusCode.IMAGE_TOO_BRIGHT,
      7: StatusCode.CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING,
      8: StatusCode.PROCESSING_NOT_STARTED
    };
    return statusMap[code] || StatusCode.PROCESSING_NOT_STARTED;
  }

  private handleError(error: SmartSpectraError): void {
    const report: ErrorReport = {
      error,
      timestamp: Date.now(),
      context: {
        mode: this.currentMode,
        isRunning: this.isRunning,
        isInitialized: this.isInitialized()
      },
      recovery: {
        canRecover: error.recoverable,
        recoveryAction: error.recoverable ? 'Retry operation' : 'Restart SDK',
        attempts: 0
      }
    };

    this.errorHistory.push(report);

    // Limit error history size
    if (this.errorHistory.length > 100) {
      this.errorHistory.shift();
    }

    // Notify error listeners
    this.errorListeners.forEach(listener => {
      try {
        listener(report);
      } catch (listenerError) {
        console.error('Error in error listener:', listenerError);
      }
    });
  }
}
