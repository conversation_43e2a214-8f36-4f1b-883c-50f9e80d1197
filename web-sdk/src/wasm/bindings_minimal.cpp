#include <emscripten/bind.h>
#include "smartspectra_wasm_minimal.hpp"

using namespace emscripten;
using namespace smartspectra_wasm;

EMSCRIPTEN_BINDINGS(smartspectra_wasm_minimal) {
    class_<SmartSpectraWASMMinimal>("SmartSpectraWASM")
        .constructor<>()

        // Configuration and initialization
        .function("initialize", &SmartSpectraWASMMinimal::Initialize)
        .function("isInitialized", &SmartSpectraWASMMinimal::IsInitialized)

        // Mode management
        .function("startSpotMode", &SmartSpectraWASMMinimal::StartSpotMode)
        .function("startContinuousMode", &SmartSpectraWASMMinimal::StartContinuousMode)
        .function("stop", &SmartSpectraWASMMinimal::Stop)
        .function("isRunning", &SmartSpectraWASMMinimal::IsRunning)

        // Frame processing
        .function("processFrame", &SmartSpectraWASMMinimal::ProcessFrame)

        // Status and diagnostics
        .function("getCurrentStatus", &SmartSpectraWASMMinimal::GetCurrentStatus)
        .function("getLastError", &SmartSpectraWASMMinimal::GetLastError)

        // Callback registration
        .function("setOnMetricsCallback", &SmartSpectraWASMMinimal::SetOnMetricsCallback)
        .function("setOnStatusCallback", &SmartSpectraWASMMinimal::SetOnStatusCallback)
        .function("setOnErrorCallback", &SmartSpectraWASMMinimal::SetOnErrorCallback)

        // Cleanup
        .function("cleanup", &SmartSpectraWASMMinimal::Cleanup)

        // Static methods
        .class_function("getVersion", &SmartSpectraWASMMinimal::GetVersion)
        .class_function("isSupported", &SmartSpectraWASMMinimal::IsSupported);

    // Status code constants for JavaScript
    constant("StatusCode_OK", 0);
    constant("StatusCode_NO_FACES_FOUND", 1);
    constant("StatusCode_MORE_THAN_ONE_FACE_FOUND", 2);
    constant("StatusCode_FACE_NOT_CENTERED", 3);
    constant("StatusCode_FACE_TOO_BIG_OR_TOO_SMALL", 4);
    constant("StatusCode_IMAGE_TOO_DARK", 5);
    constant("StatusCode_IMAGE_TOO_BRIGHT", 6);
    constant("StatusCode_CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING", 7);
    constant("StatusCode_PROCESSING_NOT_STARTED", 8);
}
